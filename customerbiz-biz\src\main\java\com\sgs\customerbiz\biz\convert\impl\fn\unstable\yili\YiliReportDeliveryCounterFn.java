package com.sgs.customerbiz.biz.convert.impl.fn.unstable.yili;

import com.alibaba.fastjson.JSON;
import com.sgs.customerbiz.biz.convert.impl.fn.base.StringOperationFn;
import com.sgs.customerbiz.biz.service.task.TaskService;
import com.sgs.customerbiz.biz.service.task.impl.handler.model.message.MessageTaskParameters;
import com.sgs.customerbiz.dbstorages.mybatis.model.TaskInfoPO;
import com.sgs.customerbiz.domain.enums.TaskStatusEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;
import java.util.function.Predicate;

/**
 * 伊利报告回传计数
 *
 * @author: shawn.yang
 * @create: 2023-09-27 16:14
 */
@Slf4j
@Component
public class YiliReportDeliveryCounterFn extends StringOperationFn {
    // 伊利的报告回传API ID,数据库修改后这里需要同步修改！！！！
    // 后续建议在SYSTEM_API表加上 API编号字段:“YiliSyncReport”
    private static final Long YILI_REPORT_DATA_SYNC_API_ID = 19L;

    private static final String action = "SyncReport";

    private final TaskService taskService;

    @Override
    protected Object invoke(Object refSystemId,Object trfNo) {
        if (refSystemId ==null || trfNo ==null){
            throw new IllegalArgumentException("refSystemId and trfNo can not be null");
        }
        if (!StringUtils.isNumeric(refSystemId.toString())){
            throw new IllegalArgumentException("refSystemId not numeric");
        }

        return this.getCount(Integer.parseInt(refSystemId.toString()),trfNo.toString()) + ".0";
    }


    private long getCount(Integer refSystemId,String trfNo){
        List<TaskInfoPO> taskInfoList = taskService.queryByExtIdWithNoTaskParametersFromExt(refSystemId, trfNo);
        if (taskInfoList.isEmpty() || taskInfoList.size() < 1){
            return 1L;
        }

        Predicate<TaskInfoPO> isSuccessReportSyncTask = (TaskInfoPO taskInfo) -> {
            String taskParameters = taskInfo.getTaskParameters();
            MessageTaskParameters messageTaskParameters = JSON.parseObject(taskParameters, MessageTaskParameters.class);
            if (messageTaskParameters == null || messageTaskParameters.getApiInfo() == null) {
                return false;
            }
            boolean taskSuccess = Objects.equals(taskInfo.getTaskStatus(), TaskStatusEnum.SUCCESS.getCode());

            return taskSuccess && messageTaskParameters.getMessage().toString().contains(action);
        };
        long count = taskInfoList.stream()
                .filter(Objects::nonNull)
                .filter(isSuccessReportSyncTask)
                .count();
        if (Objects.equals(count, 0L)) {
            return 1L;
        } else {
            return count+1;
        }
    }

    @Override
    public String getName() {
        return "yiliReportDeliveryCounter";
    }

    @Override
    public String desc() {
        return "伊利报告回传计数器";
    }


    public YiliReportDeliveryCounterFn(TaskService taskService) {
        this.taskService = taskService;
    }
}
