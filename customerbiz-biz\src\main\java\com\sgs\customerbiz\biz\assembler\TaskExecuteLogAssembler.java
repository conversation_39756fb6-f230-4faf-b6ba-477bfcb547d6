package com.sgs.customerbiz.biz.assembler;

import cn.hutool.core.net.NetUtil;
import com.alibaba.fastjson.JSON;
import com.sgs.customerbiz.biz.service.task.impl.handler.TaskExecResult;
import com.sgs.customerbiz.dbstorages.mybatis.model.TaskExecuteLogPO;
import com.sgs.customerbiz.dbstorages.mybatis.model.TaskInfoPO;
import org.apache.commons.lang3.StringUtils;

import java.util.Date;

import static com.sgs.customerbiz.core.constants.Constants.USER_DEFAULT;

/**
 * @author: shawn.yang
 * @create: 2023-05-09 16:34
 */
public final class TaskExecuteLogAssembler {

    //todo PO
    public static TaskExecuteLogPO assembleTaskExecuteLog(TaskInfoPO taskInfo, TaskExecResult taskExecResult){
        TaskExecuteLogPO taskExecuteLog = new TaskExecuteLogPO();
        taskExecuteLog.setTaskId(taskInfo.getTaskId());
        taskExecuteLog.setGroupKey(taskInfo.getGroupKey());
        taskExecuteLog.setExtId(taskInfo.getExtId());
        taskExecuteLog.setHandlerName(taskInfo.getHandlerName());
        taskExecuteLog.setTaskParameters(taskInfo.getTaskParameters());
        taskExecuteLog.setExecuteResult(JSON.toJSONString(taskExecResult));
        taskExecuteLog.setExecuteIp(StringUtils.defaultString(NetUtil.getLocalhostStr()));
        taskExecuteLog.setCreatedBy(USER_DEFAULT);
        taskExecuteLog.setCreatedDate(new Date());

        return taskExecuteLog;
    }


}
