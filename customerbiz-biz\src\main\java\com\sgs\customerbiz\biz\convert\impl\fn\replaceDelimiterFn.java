package com.sgs.customerbiz.biz.convert.impl.fn;

import com.google.common.base.Joiner;
import com.sgs.customerbiz.biz.convert.impl.fn.base.StringOperationFn;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Objects;
import java.util.stream.Stream;

@Component
public class replaceDelimiterFn extends StringOperationFn {


    @Override
    protected Object invoke(Object arg1, Object arg2, Object arg3) {
        if(Objects.isNull(arg1)) {
            return "";
        }
        if (Objects.isNull(arg3)) {
            throw new IllegalArgumentException("arg1 and arg2 and arg3 is null");
        }

        String value = arg1.toString();
        String oriDelimiter = (Objects.isNull(arg2) || StringUtils.isBlank(arg2.toString()))? ",": arg2.toString();
        String replace = arg3.toString();

        return Joiner.on(replace).join(Stream.of(value.split(oriDelimiter)).filter(StringUtils::isNotBlank).toArray());
    }

    @Override
    public String getName() {
        return "replaceDelimiter";
    }

    @Override
    public String desc() {
        return "replaceDelimiterFn";
    }
}
