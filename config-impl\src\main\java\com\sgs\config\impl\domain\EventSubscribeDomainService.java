package com.sgs.config.impl.domain;

import com.sgs.config.impl.convertor.EventSubscribeConvertor;
import com.sgs.config.impl.domain.domainobject.EventSubscribeDO;
import com.sgs.config.impl.manager.EventSubscribeManager;
import com.sgs.customerbiz.dbstorages.mybatis.model.EventSubscribePO;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * @author: shawn.yang
 * @create: 2023-05-08 16:20
 */
@Service
public class EventSubscribeDomainService {

    private final EventSubscribeManager eventSubscribeManager;


    public List<EventSubscribeDO> findBySubsciberAndRefSystemId(Integer subscriber, Integer systemId){
        List<EventSubscribePO> eventSubscribeList = eventSubscribeManager.getBySubsciberAndRefSystemId(subscriber, systemId);

        return EventSubscribeConvertor.convertToDO(eventSubscribeList);
    }


    public List<EventSubscribeDO> get(Integer refSystemId, Integer event){
        List<EventSubscribePO> eventSubscribeList = eventSubscribeManager.get(refSystemId, event);
        return EventSubscribeConvertor.convertToDO(eventSubscribeList);
    }

    public List<EventSubscribeDO> get(Integer refSystemId, Integer subscriber, Integer eventCode){
        List<EventSubscribePO> eventSubscribeList = eventSubscribeManager.get(refSystemId, subscriber, eventCode);
        return EventSubscribeConvertor.convertToDO(eventSubscribeList);
    }

    public List<EventSubscribeDO> getBySubscriber(Optional<Long> apiId, Integer subscriber, Integer event){
        List<EventSubscribePO> eventSubscribeList = eventSubscribeManager.getBySubscriber(apiId, subscriber, event);
        return EventSubscribeConvertor.convertToDO(eventSubscribeList);
    }



    public EventSubscribeDomainService(EventSubscribeManager eventSubscribeManager) {
        this.eventSubscribeManager = eventSubscribeManager;
    }
}
