package com.sgs.customerbiz.biz.convert.impl.fn.unstable.walmart;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.sgs.customerbiz.biz.convert.impl.fn.base.StringOperationFn;
import com.sgs.customerbiz.model.trf.dto.TrfCustomerContactDTO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Component
public class ContactsFn extends StringOperationFn {

    @Override
    public String getName() {
        return "walmartContacts";
    }

    @Override
    public String desc() {
        return "Split contact emails and create separate contact records";
    }

    @Override
    protected Object invoke(Object arg1) {
        if (arg1 == null) {
            return Collections.emptyList();
        }

        // 将输入参数转换为TrfCustomerContactDTO列表
        List<TrfCustomerContactDTO> contacts;
        try {
            contacts = JSONArray.parseArray(JSON.toJSONString(arg1), TrfCustomerContactDTO.class);
        } catch (Exception e) {
            return Collections.emptyList();
        }

        // 使用flatMap处理每个联系人
        return  contacts.stream()
                .filter(Objects::nonNull)
                .filter(contact -> StringUtils.isNotBlank(contact.getContactEmail()))
                .flatMap(contact -> {
                    // 分割邮箱地址
                    String[] emails = contact.getContactEmail().split(",");
                    return Arrays.stream(emails)
                            .map(String::trim)
                            .filter(StringUtils::isNotBlank)
                            .map(email -> {
                                JSONObject jsonObject = new JSONObject();
                                jsonObject.put("name", contact.getContactName());
                                jsonObject.put("email", email);
                                return jsonObject;
                            });
                })
                .distinct() // 添加distinct()去重
                .collect(Collectors.toList());
    }
}
