package com.sgs.customerbiz.biz.convert.impl.fn;

import com.alibaba.fastjson.JSON;
import com.sgs.customerbiz.biz.convert.impl.fn.base.StringOperationFn;
import com.sgs.customerbiz.model.trf.dto.TrfConditionDTO;
import com.sgs.customerbiz.model.trf.dto.TrfReportMatrixDTO;
import com.sgs.customerbiz.model.trf.dto.TrfTestLineDTO;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Stream;

@Component
public class UNIQLOGetTestCondition extends StringOperationFn {

    @Override
    public Object invoke(Object[] args) {
        if(args == null || args.length != 5 || Stream.of(args).anyMatch(Objects::isNull)) {
            return null;
        }
        TrfReportMatrixDTO matrix = JSON.parseObject(JSON.toJSONString(args[0]), TrfReportMatrixDTO.class);
        String conditionInstanceId = args[1].toString();
        TrfTestLineDTO testLine = JSON.parseObject(JSON.toJSONString(args[2]), TrfTestLineDTO.class);
        //testLineId -> conditionId -> conditionType
        Map<String, Map<String, String>> testLineMapping = (Map<String, Map<String, String>>)args[3];
        String conditionType = args[4].toString();

        //    通过`_testMatrix` + `_conditionInstanceId` 得到conditionId ? 会是多个吗？
        //    通过`_testLine` + `得到conditionId` 得到 ConditionType
        //    如果`ConditionType`与 参数相等，输出 `_testMatrix。conditionList[conditionInstanceId=@._conditionInstanceId].conditionDesc`
        String testLineId = Optional.ofNullable(testLine.getTestLineId()).map(Object::toString).orElse("");
        if(!testLineMapping.containsKey(testLineId)) {
            return "";
        }
        Optional<TrfConditionDTO> conditionDTO = Optional.ofNullable(matrix.getConditionList())
                .flatMap(conditionList -> conditionList.stream()
                        .filter(condition -> Objects.nonNull(condition.getConditionInstanceId()))
                        .filter(condition -> condition.getConditionInstanceId().equals(conditionInstanceId))
                        .findFirst()
                );
        Optional<String> conditionId = conditionDTO
                .map(TrfConditionDTO::getConditionId)
                .map(Object::toString);
        if(!conditionId.isPresent()) {
            return "";
        }

        Map<String, String> conditionMap = testLineMapping.get(testLineId);
        String conditionIdValue = conditionId.get();
        if(!conditionMap.containsKey(conditionIdValue)) {
            return "";
        }
        Boolean matchType = Optional.ofNullable(conditionMap.get(conditionIdValue)).map(type -> type.equals(conditionType)).orElse(false);
        return matchType ? conditionDTO.map(TrfConditionDTO::getConditionDesc).orElse("") : "";
    }


    @Override
    public String getName() {
        return "uNIQLOGetTestCondition";
    }

    @Override
    public String desc() {
        return "uNIQLOGetTestCondition";
    }
}
