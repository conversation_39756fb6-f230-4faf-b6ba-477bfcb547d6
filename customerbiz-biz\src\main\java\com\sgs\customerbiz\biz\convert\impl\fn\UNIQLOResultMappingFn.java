package com.sgs.customerbiz.biz.convert.impl.fn;

import cn.hutool.core.util.ArrayUtil;
import com.sgs.customerbiz.biz.convert.impl.fn.base.StringOperationFn;
import com.sgs.framework.tool.utils.Func;
import org.springframework.stereotype.Component;

import java.util.*;


@Component
public class UNIQLOResultMappingFn extends StringOperationFn {

    private static final Map<String, String> mappingMap = new HashMap<>();
    static {
        mappingMap.put("Pass", "OK");
        mappingMap.put("Exempt", "OK");
        mappingMap.put("NA", "OK");
        mappingMap.put("Fail", "NG");
        mappingMap.put("Inconclusive", "NG");
        mappingMap.put("Data Only", " ");
    }

    @Override
    public Object invoke(Object[] args) {
        if (ArrayUtil.isEmpty(args)) {
            return null;
        }
        Object key = args[0];
        if (Func.isEmpty(key)) {
            return null;
        }

        String conclusionCode = key.toString();
        return mappingMap.get(conclusionCode);
    }

    @Override
    public String getName() {
        return "uNIQLOResultMappingFn";
    }

    @Override
    public String desc() {
        return "uNIQLOResultMappingFn";
    }
}
