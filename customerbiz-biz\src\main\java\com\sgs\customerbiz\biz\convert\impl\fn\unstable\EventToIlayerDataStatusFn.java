package com.sgs.customerbiz.biz.convert.impl.fn.unstable;

import com.sgs.customerbiz.biz.convert.impl.fn.base.UnstableConvertFn;
import com.sgs.customerbiz.domain.domainevent.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 根据event转成与ilayer通信的DataStatus字段
 * @author: shawn.yang
 * @create: 2023-08-17 16:08
 */
@Slf4j
@Component
public class EventToIlayerDataStatusFn implements UnstableConvertFn {

    @Override
    public Object invoke(Object[] args) {
        if (args ==null || args.length != 1) {
            throw new IllegalArgumentException(String.format("incorrect args length:expected 1,actual %d",args ==null?0:args.length));
        }

        Object eventName = args[0];

        if (eventName ==null){
            throw new IllegalArgumentException("arg0:eventName can not be null");
        }

        return toDataStatus(eventName.toString());
    }

    private String toDataStatus(String eventName){
//        New/ToOrder/Confirmed/Testing/Reporting/Completed/Closed/Cancelled/BindTrf/UnbindTrf/Pending/UnPending/Revise
        if (TrfNewEvent.class.getName().equals(eventName)){
            return "New";
        }else if (TrfToOrderEvent.class.getName().equals(eventName)){
            return "ToOrder";
        }else if (TrfConfirmedEvent.class.getName().equals(eventName)){
            return "Confirmed";
        }else if (TrfTestingEvent.class.getName().equals(eventName)){
            return "Testing";
        }else if (TrfReportingEvent.class.getName().equals(eventName)){
            return "Reporting";
        }else if (TrfCompletedEvent.class.getName().equals(eventName)||TrfOrderCompletedEvent.class.getName().equals(eventName)){
            return "Completed";
        }else if (TrfClosedEvent.class.getName().equals(eventName)){
            return "Closed";
        }else if (TrfPreOrderCancelEvent.class.getName().equals(eventName) || TrfCustomerCancelEvent.class.getName().equals(eventName)){
            return "Cancelled";
        }else if (TrfUnBindEvent.class.getName().equals(eventName)){
            return "UnbindTrf";
        }else if(TrfPendingEvent.class.getName().equals(eventName)) {
            return "Pending";
        }else if (TrfUnPendingEvent.class.getName().equals(eventName)) {
            return "UnPending";
        }else {
            log.error("convert toDataStatus error. unsupported event:{}",eventName.getClass().getName());
            throw new UnsupportedOperationException("unsupported event");
        }
    }

    @Override
    public String getName() {
        return "eventToIlayerDataStatus";
    }

    @Override
    public String desc() {
        return "根据event转成与ilayer通信的DataStatus字段";
    }
}
