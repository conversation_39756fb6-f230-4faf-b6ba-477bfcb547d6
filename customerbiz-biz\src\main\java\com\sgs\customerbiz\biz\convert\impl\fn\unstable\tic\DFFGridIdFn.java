package com.sgs.customerbiz.biz.convert.impl.fn.unstable.tic;

import cn.hutool.core.collection.CollUtil;
import com.sgs.customerbiz.biz.convert.impl.fn.base.UnstableConvertFn;
import com.sgs.customerbiz.biz.convert.impl.fn.base.StringOperationFn;
import com.sgs.customerbiz.domain.domainservice.TrfDomainService;
import com.sgs.customerbiz.integration.ConfigClient;
import com.sgs.customerbiz.integration.LocalILayerClient;
import com.sgs.customerbiz.model.ext.dto.req.DffInfoReq;
import com.sgs.customerbiz.model.ext.dto.rsp.QueryDffDefaultRsp;
import com.sgs.framework.core.base.CustomResult;
import com.sgs.framework.model.enums.RefSystemIdEnum;
import com.sgs.framework.tool.utils.Func;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * @author: shawn.yang
 * @create: 2023-06-13 12:06
 */
@Component
public class DFFGridIdFn extends StringOperationFn implements UnstableConvertFn {
    @Value("#{${convert.dff.grid.map}}")
    private Map<String,String> gridMap;

    @Autowired
    private LocalILayerClient localILayerClient;

    @Autowired
    private ConfigClient configClient;

    @Override
    public Object invoke(Object[] args) {
        if (args.length != 3){
            throw new IllegalArgumentException();
        }
        Object refSystemId = args[0];
        Object productCateGory = args[1];
        Object buCode = args[2];
        if (buCode ==null){
            return null;
        }
        DffInfoReq dffInfoReq = new DffInfoReq();
        dffInfoReq.setBuCode(buCode.toString());

        if (Func.isNotBlank(productCateGory.toString())) {
            Integer refSystemIdInt = Integer.parseInt(refSystemId.toString());
            if (Objects.equals(refSystemIdInt, RefSystemIdEnum.TIC.getRefSystemId())) {
                if (Objects.equals(productCateGory.toString(), "SL-HZ-WB-REGULAR")) {
                    dffInfoReq.setProductLineCategory("TX");
                } else {
                    List<String> customerGroupCodeByRefSystemId = configClient.getCustomerGroupCodeByRefSystemId(Integer.parseInt(refSystemId.toString()));
                    dffInfoReq.setCustomerGroupCode(CollUtil.get(customerGroupCodeByRefSystemId, 0));
                }
            } else {
                dffInfoReq.setProductLineCategory(productCateGory.toString());
            }
        } else {
            if (Func.isEmpty(refSystemId)) {
                return null;
            }
            List<String> customerGroupCodeByRefSystemId = configClient.getCustomerGroupCodeByRefSystemId(Integer.parseInt(refSystemId.toString()));
            dffInfoReq.setCustomerGroupCode(CollUtil.get(customerGroupCodeByRefSystemId, 0));
        }
        CustomResult<QueryDffDefaultRsp> queryDffDefaultRspCustomResult = localILayerClient.queryDFFDefault(dffInfoReq);
        QueryDffDefaultRsp data = queryDffDefaultRspCustomResult.getData();
        return data.getGridFormId();
    }

    @Override
    public String getName() {
        return "dffGridId";
    }

    @Override
    public String desc() {
        return null;
    }
}
