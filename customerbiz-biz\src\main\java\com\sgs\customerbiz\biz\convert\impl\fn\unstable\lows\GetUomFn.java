package com.sgs.customerbiz.biz.convert.impl.fn.unstable.lows;

import com.sgs.customerbiz.biz.convert.impl.fn.base.UnstableConvertFn;
import com.sgs.framework.tool.utils.Func;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Date 2024/1/9 16:18
 */
@Slf4j
@Component
public class GetUomFn implements UnstableConvertFn {
    @Override
    public Object invoke(Object[] args) {
        if (Func.isEmpty(args) || args.length != 2) {
            return null;
        }
        if (Func.isEmpty(args[0])) {
            return null;
        }

        String arg2 = args[1].toString();
        switch (arg2) {
            case "weight":
                return "lb.";
            default:
                return "in";
        }
    }

    @Override
    public String getName() {
        return "getUomFn";
    }

    @Override
    public String desc() {
        return "getUomFn";
    }
}
