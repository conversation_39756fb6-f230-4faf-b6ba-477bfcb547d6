package com.sgs.customerbiz.biz.convert.impl.fn;

import com.alibaba.fastjson.JSONObject;
import com.sgs.customerbiz.biz.convert.impl.fn.base.UnstableConvertFn;
import com.sgs.framework.tool.utils.Func;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Date 2024/1/9 16:18
 */
@Slf4j
@Component
public class GetTrfExtFieldsFn implements UnstableConvertFn {
    @Override
    public Object invoke(Object[] args) {
        if (Func.isEmpty(args)) {
            return null;
        }
        if (Func.isEmpty(args[0])) {
            return null;
        }

        String arg1 = args[0].toString();
        String arg2 = args[1].toString();
        String arg3 = args[2].toString();

        JSONObject jsonObject = JSONObject.parseObject(arg1);
        JSONObject lowes = jsonObject.getJSONObject(arg2);
        return lowes.get(arg3);

    }

    @Override
    public String getName() {
        return "getTrfExtFieldsFn";
    }

    @Override
    public String desc() {
        return "getTrfExtFieldsFn";
    }
}
