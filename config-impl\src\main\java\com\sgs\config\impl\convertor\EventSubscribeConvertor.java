package com.sgs.config.impl.convertor;

import com.sgs.config.api.dto.EventSubscribeDTO;
import com.sgs.config.impl.domain.domainobject.EventSubscribeDO;
import com.sgs.customerbiz.dbstorages.mybatis.model.EventSubscribePO;
import org.springframework.beans.BeanUtils;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @author: shawn.yang
 * @create: 2023-05-08 17:23
 */
public final class EventSubscribeConvertor {

    public static List<EventSubscribeDTO> convertToDTO(List<EventSubscribeDO> subscribeDOList){
        if (subscribeDOList ==null){
            return Collections.emptyList();
        }
        return subscribeDOList.stream().map(subscribeDO->{
            EventSubscribeDTO eventSubscribeDTO = new EventSubscribeDTO();
            BeanUtils.copyProperties(subscribeDO,eventSubscribeDTO);
            return eventSubscribeDTO;
        }).collect(Collectors.toList());

    }

    public static List<EventSubscribeDO> convertToDO(List<EventSubscribePO> subscribeDOList){
        if (subscribeDOList ==null){
            return Collections.emptyList();
        }
        return subscribeDOList.stream().map(subscribePO->{
            EventSubscribeDO eventSubscribeDO = new EventSubscribeDO();
            BeanUtils.copyProperties(subscribePO,eventSubscribeDO);
            return eventSubscribeDO;
        }).collect(Collectors.toList());
    }

}
