package com.sgs.customerbiz.biz.convert.impl.fn;

import com.alibaba.fastjson.JSONObject;
import com.sgs.customerbiz.biz.convert.impl.fn.base.StringOperationFn;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Component
public class ObjectToListFn extends StringOperationFn {

    @Override
    protected Object invoke(Object arg1,Object arg2, Object arg3, Object arg4) {
        List<Object> list = new ArrayList<>();
        if(Objects.isNull(arg3) || Objects.isNull(arg4)) {
            throw new IllegalArgumentException("arg3 and arg4 must not be null");
        }
        JSONObject obj = new JSONObject();
        obj.put(arg3.toString(), arg1);
        obj.put(arg4.toString(), arg2);
        list.add(obj);
        return list;
    }

    @Override
    public String getName() {
        return "objectToList";
    }

    @Override
    public String desc() {
        return "objectToListFn";
    }
}
