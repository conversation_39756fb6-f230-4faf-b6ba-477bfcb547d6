package com.sgs.config.api.dto.req;

import com.sgs.framework.core.base.BaseProductLine;

/**
 * @Desc
 * <AUTHOR>
 * @date 2023/12/7 11:06
 */
public class ConfigBaseRequest extends BaseProductLine {

    private String requestId;

    private Integer systemId;

    private String labCode;

    private Integer labId;

    private Integer buId;

    private String buCode;

    public String getRequestId() {
        return requestId;
    }

    public void setRequestId(String requestId) {
        this.requestId = requestId;
    }

    public Integer getSystemId() {
        return systemId;
    }

    public void setSystemId(Integer systemId) {
        this.systemId = systemId;
    }

    public String getLabCode() {
        return labCode;
    }

    public void setLabCode(String labCode) {
        this.labCode = labCode;
    }

    public Integer getLabId() {
        return labId;
    }

    public void setLabId(Integer labId) {
        this.labId = labId;
    }

    public Integer getBuId() {
        return buId;
    }

    public void setBuId(Integer buId) {
        this.buId = buId;
    }

    public String getBuCode() {
        return buCode;
    }

    public void setBuCode(String buCode) {
        this.buCode = buCode;
    }
}
