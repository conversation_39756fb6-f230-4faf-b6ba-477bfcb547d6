package com.sgs.customerbiz.biz.convert.impl.fn.unstable.septwolves;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.sgs.customerbiz.biz.convert.impl.fn.base.UnstableConvertFn;
import com.sgs.customerbiz.model.trf.enums.ConclusionCodeType;
import com.sgs.framework.model.enums.LanguageType;
import com.sgs.framework.tool.utils.Func;
import com.sgs.testdatabiz.facade.model.dto.rd.report.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2024/1/9 16:18
 */
@Slf4j
@Component
public class GetPrefixAFn implements UnstableConvertFn {
    @Override
    public Object invoke(Object[] args) {
        if (Func.isEmpty(args)) {
            return null;
        }
        Object arg = args[0];
        if (Func.isEmpty(arg)) {
            return null;
        }
        RdCitationDTO rdCitationDTO = JSONObject.parseObject(arg.toString(), RdCitationDTO.class);
        if (Func.isEmpty(rdCitationDTO)) {
            return null;
        }
        List<RdCitationLanguageDTO> languageList = rdCitationDTO.getLanguageList();
        if (Func.isEmpty(languageList)) {
            return rdCitationDTO.getCitationFullName();
        }
        RdCitationLanguageDTO rdCitationLanguageDTO = languageList.stream().filter(l -> Objects.equals(l.getLanguageId(), LanguageType.Chinese.getLanguageId())).findFirst().orElse(null);
        if (Func.isEmpty(rdCitationLanguageDTO)) {
            return rdCitationDTO.getCitationFullName();
        }
        return rdCitationLanguageDTO.getCitationFullName();
    }

    @Override
    public String getName() {
        return "getPrefixAFn";
    }

    @Override
    public String desc() {
        return "getPrefixAFn";
    }
}
