package com.sgs.customerbiz.biz.convert.impl.fn;

import com.alibaba.fastjson.JSONPath;
import com.sgs.customerbiz.biz.convert.impl.fn.base.StringOperationFn;
import com.sgs.framework.tool.utils.Func;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @author: shawn.yang
 * @create: 2023-05-22 14:03
 */
@Component("joinConvertFn")
public class JoinFn extends StringOperationFn {
    @Override
    public Object invoke(Object[] args) {
        if (args == null || args.length == 0) {
            return null;
        }

        if (args.length >= 2) {
            String delimiter = subtractQuote(args[0].toString());

            Object[] objects = Arrays.copyOfRange(args, 1, args.length);
            List<Object> list = new ArrayList<>();
            for (Object object : objects) {
                if (object instanceof List) {
                    list.addAll((List) object);
                } else if (Func.isNotEmpty(object)) {
                    list.add(object);
                }
            }
            if (list.isEmpty()) {
                return null;
            }
            return list.stream().map(arg -> subtractQuote(arg == null ? "" : arg.toString())).collect(Collectors.joining(delimiter));
        }

        throw new IllegalArgumentException(String.format("incorrect args length:expected greater than 2 ,actual %d", args.length));
    }

    public static void main(String[] args) {
        String s = "{\"baseInfo\":{\"reportForm\":2,\"isUrgent\":1,\"modifyDate\":\"\",\"city\":\"杭州市\",\"userPhone\":\"***********\",\"labName\":\"SGS杭州纺织品实验室\",\"discountAmount\":0,\"userName\":\"Eva\",\"realAmount\":0,\"orderAmount\":0,\"bu\":\"SL\",\"province\":\"浙江省\",\"testCycle\":0,\"reportLua\":2,\"businessLine\":\"\",\"csCode\":\"Sandy-ZJ_Wang\",\"sampleRequirements\":\"\",\"subBuCode\":\"SL-DEFAULT\",\"auditCode\":\"\",\"ugrentAmount\":0},\"applicationAttr\":[{\"areaCode\":\"labInfo\",\"attrCode\":\"linkPhone\",\"attrValue\":\"0571-********\",\"attrName\":\"linkPhone\"},{\"areaCode\":\"labInfo\",\"attrCode\":\"town\",\"attrValue\":\"滨江区\",\"attrName\":\"town\"},{\"areaCode\":\"labInfo\",\"attrCode\":\"city\",\"attrValue\":\"杭州市\",\"attrName\":\"city\"},{\"areaCode\":\"labInfo\",\"attrCode\":\"businessLineName\",\"attrValue\":\"\",\"attrName\":\"businessLineName\"},{\"areaCode\":\"labInfo\",\"attrCode\":\"postcode\",\"attrValue\":\"\",\"attrName\":\"postcode\"},{\"areaCode\":\"labInfo\",\"attrCode\":\"labName\",\"attrValue\":\"SGS杭州纺织品实验室\",\"attrName\":\"labName\"},{\"areaCode\":\"labInfo\",\"attrCode\":\"labAddress\",\"attrValue\":\"滨安路1180号华业科技园4号楼3楼（TIC国标组）\",\"attrName\":\"labAddress\"},{\"areaCode\":\"labInfo\",\"attrCode\":\"categoryName\",\"attrValue\":\"全国纺织品测试服务部(国标)\",\"attrName\":\"categoryName\"},{\"areaCode\":\"labInfo\",\"attrCode\":\"labAddressShow\",\"attrValue\":\"浙江省杭州市滨江区滨安路1180号华业科技园4号楼3楼（TIC国标组）\",\"attrName\":\"labAddressShow\"},{\"areaCode\":\"labInfo\",\"attrCode\":\"province\",\"attrValue\":\"浙江省\",\"attrName\":\"province\"},{\"areaCode\":\"labInfo\",\"attrCode\":\"wxpayChannel\",\"attrValue\":\"HZ_WX\",\"attrName\":\"wxpayChannel\"},{\"areaCode\":\"labInfo\",\"attrCode\":\"labCode\",\"attrValue\":\"HZ SL\",\"attrName\":\"labCode\"},{\"areaCode\":\"labInfo\",\"attrCode\":\"accountNo\",\"attrValue\":\"********\",\"attrName\":\"accountNo\"},{\"areaCode\":\"labInfo\",\"attrCode\":\"catName\",\"attrValue\":\"全国纺织品测试服务部(国标)\",\"attrName\":\"catName\"},{\"areaCode\":\"labInfo\",\"attrCode\":\"labId\",\"attrValue\":\"582\",\"attrName\":\"labId\"},{\"areaCode\":\"labInfo\",\"attrCode\":\"subBu\",\"attrValue\":\"\",\"attrName\":\"subBu\"},{\"areaCode\":\"labInfo\",\"attrCode\":\"businessLineId\",\"attrValue\":\"0\",\"attrName\":\"businessLineId\"},{\"areaCode\":\"labInfo\",\"attrCode\":\"categoryId\",\"attrValue\":\"63\",\"attrName\":\"categoryId\"},{\"areaCode\":\"labInfo\",\"attrCode\":\"linkPerson\",\"attrValue\":\"TIC-王霞\",\"attrName\":\"linkPerson\"},{\"areaCode\":\"labInfo\",\"attrCode\":\"platformCode\",\"attrValue\":\"\",\"attrName\":\"platformCode\"},{\"areaCode\":\"labInfo\",\"attrCode\":\"alipayChannel\",\"attrValue\":\"HZ_ZFB\",\"attrName\":\"alipayChannel\"},{\"areaCode\":\"ticAppFormDTO\",\"attrCode\":\"methodsUsed\",\"attrValue\":\"\",\"attrName\":\"采用标准\"},{\"areaCode\":\"ticAppFormReportInfo\",\"attrCode\":\"isShanghaiUploadProject\",\"attrValue\":\"\",\"attrName\":\"是否属于上海市环境监测社会化服务监管系统上传项目\"}],\"orderNo\":\"TICSL240919110227PSQ9\",\"attachments\":[{\"fileName\":\"1.png\",\"attType\":\"110\",\"orderNo\":\"TICSL240919110227PSQ9\",\"cloudId\":\"TIC/2023/12/1_1701679625835.png\",\"uploadType\":2,\"reportNo\":\"\",\"fileId\":\"eb4b0692-99f1-453d-b2b0-3b73056c01b5\",\"downloadFile_hide\":\"attachmentUrl_hide\",\"attachmentUrl\":\"https://sgssacne2sgsmartarchprod.blob.core.chinacloudapi.cn/cn-arch-prd/TIC/2023/12/1_1701679625835.png?sig=V6HCFoh%2BDeBQ2hJ9ByOtaEaWhatUADiNBN4wC2OQjoU%3D&se=2024-09-23T10%3A15%3A40Z&sv=2017-07-29&sp=r&sr=b\",\"trfNo_hide\":\"TICSL240919110227PSQ9\"}],\"applicationForm\":{\"linkPhone\":\"***********\",\"province\":\"浙江省\",\"town\":\"钱塘区\",\"city\":\"杭州市\",\"companyName\":\"宁波维强进出口有限公司\",\"companyAddr\":\"10号大街300-11号东方科技城3幢18楼\",\"companyAddrEn\":\"\",\"companyNameEn\":\"\",\"linkPerson\":\"Eva\",\"testMemo\":\"耐酚黄变GB/T 29778-2013，PH 值 GB/T 7573-2009 不体现产品等级和安全技术级别。\",\"linkEmail\":\"<EMAIL>\"},\"memos\":[],\"relateOrderNo\":\"\",\"delivers\":[{\"receivePhone\":\"0571-********\",\"receiveName\":\"TIC-王霞\",\"receiveTown\":\"滨江区\",\"expressNo\":\"SF0260375634777\",\"receiveCity\":\"杭州市\",\"receiveEmail\":\"\",\"receiveProvince\":\"浙江省\",\"deliverType\":10}],\"invoice\":{\"bankNumber\":\"**************\",\"registerPhone\":\"0574-********\",\"invoiceType\":1,\"registerAddr\":\"浙江省宁波市鄞州区启明路655弄90号\",\"bankAddr\":\"平安银行股份有限公司宁波明州支行\",\"taxNo\":\"91330212MA2AH86R3N\",\"invoiceTitle\":\"宁波强维进出口有限公司\"},\"sampleForm\":[{\"sampleKey\":\"sampleName\",\"sampleNo\":\"8c743e9da9f5\",\"sampleValue\":\"PU乳白膜0.02mm\",\"groupNo\":\"18361621e3f0d2b3\",\"sampleKeyName\":\"样品名称\"},{\"sampleKey\":\"sampleNum\",\"sampleNo\":\"8c743e9da9f5\",\"sampleValue\":\"1\",\"groupNo\":\"18361621e3f0d2b3\",\"sampleKeyName\":\"样品数量\"},{\"sampleKey\":\"material\",\"sampleNo\":\"8c743e9da9f5\",\"sampleValue\":\"\",\"groupNo\":\"18361621e3f0d2b3\",\"sampleKeyName\":\"成分/材质\"},{\"sampleKey\":\"colour\",\"sampleNo\":\"8c743e9da9f5\",\"sampleValue\":\"乳白色\",\"groupNo\":\"18361621e3f0d2b3\",\"sampleKeyName\":\"颜色\"},{\"sampleKey\":\"manufacturerName\",\"sampleNo\":\"8c743e9da9f5\",\"sampleValue\":\"\",\"groupNo\":\"18361621e3f0d2b3\",\"sampleKeyName\":\"生产商名称\"},{\"sampleKey\":\"styleNo\",\"sampleNo\":\"8c743e9da9f5\",\"sampleValue\":\"B\",\"groupNo\":\"18361621e3f0d2b3\",\"sampleKeyName\":\"款号\"},{\"sampleKey\":\"brandName\",\"sampleNo\":\"8c743e9da9f5\",\"sampleValue\":\"\",\"groupNo\":\"18361621e3f0d2b3\",\"sampleKeyName\":\"品牌名称\"},{\"sampleKey\":\"isReportShow\",\"sampleNo\":\"8c743e9da9f5\",\"sampleValue\":\"否\",\"groupNo\":\"18361621e3f0d2b3\",\"sampleKeyName\":\"显示在报告中样品信息栏\"},{\"sampleKey\":\"specialInfo\",\"sampleNo\":\"8c743e9da9f5\",\"sampleValue\":\"\",\"groupNo\":\"18361621e3f0d2b3\",\"sampleKeyName\":\"特殊信息备注\"},{\"sampleKey\":\"safetyTechnicalLevel\",\"sampleNo\":\"8c743e9da9f5\",\"sampleValue\":\"B类\",\"groupNo\":\"18361621e3f0d2b3\",\"sampleKeyName\":\"安全技术级别\"},{\"sampleKey\":\"productGrade\",\"sampleNo\":\"8c743e9da9f5\",\"sampleValue\":\"服装家纺类&&&&&一等品\",\"groupNo\":\"18361621e3f0d2b3\",\"sampleKeyName\":\"产品等级\"}],\"items\":[{\"disPrice\":\"\",\"detailId\":2892861,\"buyNums\":1,\"cmaLab\":\"\",\"itemAlias\":\"\",\"priceShow\":\"165.00\",\"testMemo\":\"\",\"standardCode\":\"GB18401\",\"itemId\":\"\",\"itemName\":\"pH值\",\"unit\":\"\",\"otherExplain\":\"\",\"price\":165,\"testDays\":\"\",\"sampleRequirements\":\"\",\"cnasLab\":\"\",\"labelName\":\"\"},{\"disPrice\":\"\",\"detailId\":2892862,\"buyNums\":1,\"cmaLab\":\"\",\"itemAlias\":\"\",\"priceShow\":\"185.00\",\"testMemo\":\"\",\"standardCode\":\"GB/T 29778\",\"itemId\":\"\",\"itemName\":\"酚醛泛黄色牢度\",\"unit\":\"\",\"otherExplain\":\"\",\"price\":185,\"testDays\":\"\",\"sampleRequirements\":\"\",\"cnasLab\":\"\",\"labelName\":\"\"},{\"disPrice\":\"\",\"detailId\":2892863,\"buyNums\":\"\",\"cmaLab\":\"\",\"itemAlias\":\"\",\"priceShow\":\"\",\"testMemo\":\"\",\"standardCode\":\"\",\"itemId\":\"\",\"itemName\":\"自选\",\"unit\":\"\",\"otherExplain\":\"\",\"price\":\"\",\"testDays\":\"\",\"sampleRequirements\":\"\",\"cnasLab\":\"\",\"labelName\":\"\"}],\"orderNoTic\":\"TICSL240919110227PSQ9\",\"packageBarcode\":\"TICSL240919110227PSQ9\",\"batchNo\":null,\"createdDate\":\"2024-09-23 16:15:40\",\"createdBy\":\"Sally_Duan\",\"labType\":\"HZ SL\",\"groupId_hide\":0,\"dffFormId\":null,\"gridFormId\":null,\"trfNo_hide\":\"TICSL240919110227PSQ9\",\"list_row_id_hide\":\"60aa8065-5e37-4cff-a2b0-773a8ddb5f1d\",\"id_checkbox\":\"\"}";
        Object eval = JSONPath.eval(s, "$.sampleForm[sampleKey='sampleNum'][sampleNo='23123'].sampleValue");
        System.out.println(eval);
    }

    @Override
    public String getName() {
        return "join";
    }

    @Override
    public String desc() {
        return "给一组值之间添加指定字符串";
    }
}
