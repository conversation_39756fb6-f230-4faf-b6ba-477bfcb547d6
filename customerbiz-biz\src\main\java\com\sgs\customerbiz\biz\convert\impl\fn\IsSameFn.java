package com.sgs.customerbiz.biz.convert.impl.fn;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.sgs.customerbiz.biz.convert.impl.fn.base.StringOperationFn;
import com.sgs.customerbiz.model.trf.dto.*;
import com.sgs.customerbiz.model.trf.enums.CustomerUsage;
import com.sgs.framework.tool.utils.Func;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.*;


@Slf4j
@Component
public class IsSameFn extends StringOperationFn {

    @Override
    public Object invoke(Object[] args) {
        if (args == null) {
            return 0;
        }
        Object arg = args[0];
        if (Func.isEmpty(arg)) {
            return 0;
        }
        List<TrfCustomerDTO> trfCustomerDTOS = JSONObject.parseArray(arg.toString(), TrfCustomerDTO.class);
        TrfCustomerDTO applicant = trfCustomerDTOS.stream().filter(l -> Objects.equals(l.getCustomerUsage(), CustomerUsage.Applicant.getUsage())).findFirst().orElse(new TrfCustomerDTO());
        TrfCustomerDTO payer = trfCustomerDTOS.stream().filter(l -> Objects.equals(l.getCustomerUsage(), CustomerUsage.Payer.getUsage())).findFirst().orElse(new TrfCustomerDTO());
        if (Func.isEmpty(applicant) || Func.isEmpty(applicant.getBossNo())) {
            return 0;
        }
        if (Func.isEmpty(applicant) && Func.isEmpty(payer)) {
            return 0;
        }
        if (Func.isEmpty(payer.getBossNo()) || Objects.equals(applicant.getBossNo(), payer.getBossNo())) {
            return 1;
        }
        return 0;
    }


    @Override
    public String getName() {
        return "isSameFn";
    }

    @Override
    public String desc() {
        return "isSameFn";
    }
}
