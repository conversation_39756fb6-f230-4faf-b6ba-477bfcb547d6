package com.sgs.config.impl.manager;

import com.sgs.customerbiz.dbstorages.mybatis.extmapper.todolist.EventSubscribeExtMapper;
import com.sgs.customerbiz.dbstorages.mybatis.mapper.todolist.EventSubscribeMapper;
import com.sgs.customerbiz.dbstorages.mybatis.model.EventSubscribeExample;
import com.sgs.customerbiz.dbstorages.mybatis.model.EventSubscribePO;
import com.sgs.framework.tool.utils.Func;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Optional;

/**
 * @author: shawn.yang
 * @create: 2023-05-08 16:56
 */
@Component
public class EventSubscribeManager {

    private final EventSubscribeMapper eventSubscribeMapper;

    @Autowired
    private EventSubscribeExtMapper eventSubscribeExtMapper;


    public List<EventSubscribePO> getBySubsciberAndRefSystemId(@NotNull Integer subscriber, @NotNull Integer refSystemId) {
        EventSubscribeExample example = new EventSubscribeExample();
        example.createCriteria().andSubscriberEqualTo(subscriber)
                .andRefSystemIdEqualTo(refSystemId);
        return eventSubscribeMapper.selectByExample(example);
    }


    public List<EventSubscribePO> get(@NotNull Integer refSystemId, @NotNull Integer event) {
        return eventSubscribeExtMapper.get(event, refSystemId);
    }

    public List<EventSubscribePO> get(@NotNull Integer refSystemId,@NotNull Integer subscriber,@NotNull Integer eventCode) {
        EventSubscribeExample example = new EventSubscribeExample();
        example.createCriteria().andSubscriberEqualTo(subscriber)
                .andRefSystemIdEqualTo(refSystemId).andEventCodeEqualTo(eventCode);
        return eventSubscribeMapper.selectByExample(example);
    }

    public List<String> getEventCodeByRefSystem(Integer refSystemId, Integer eventCode) {
        List<String> eventCodeList = eventSubscribeExtMapper.getEventCodeByRefSystem(refSystemId);
        return eventCodeList;
    }

    public List<EventSubscribePO> getBySubscriber(Optional<Long> apiId, @NotNull Integer subscriber, @NotNull Integer event){
        if( apiId.isPresent() ) {
            return eventSubscribeExtMapper.getBySubscriberId(event, apiId.get());
        }
        return eventSubscribeExtMapper.getBySubscriber(event,subscriber);
    }

    public EventSubscribeManager(EventSubscribeMapper eventSubscribeMapper) {
        this.eventSubscribeMapper = eventSubscribeMapper;
    }
}
