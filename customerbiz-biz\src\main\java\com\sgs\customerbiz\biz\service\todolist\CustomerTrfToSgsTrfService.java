package com.sgs.customerbiz.biz.service.todolist;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONPath;
import com.sgs.customerbiz.biz.customertrf.service.CustomerTrfDomainService;
import com.sgs.customerbiz.biz.service.importtrf.AfterConvertProcessor;
import com.sgs.customerbiz.model.trf.dto.TrfDTO;
import com.sgs.customerbiz.model.trf.dto.TrfLabContactDTO;
import org.springframework.stereotype.Service;

import java.util.Optional;

@Service
public class CustomerTrfToSgsTrfService {

    private final CustomerTrfDomainService customerTrfDomainService;


    private final AfterConvertProcessor afterConvertProcessor;

    public CustomerTrfToSgsTrfService(CustomerTrfDomainService customerTrfDomainService, AfterConvertProcessor afterConvertProcessor) {
        this.customerTrfDomainService = customerTrfDomainService;
        this.afterConvertProcessor = afterConvertProcessor;
    }

    public TrfDTO customerTrfContentToSGSTrf(Integer refSystemId, String content) {
        JSONObject contentObject = JSON.parseObject(content);
        Integer systemId = (Integer) Optional.ofNullable(JSONPath.eval(contentObject, "$.__sgs_platform_request_context.systemId")).orElse(null);
        String labCode = Optional.ofNullable(JSONPath.eval(contentObject, "$.__sgs_platform_request_context.labCode")).map(Object::toString).orElse(null);
        String buCode = Optional.ofNullable(JSONPath.eval(contentObject, "$.__sgs_platform_request_context.buCode")).map(Object::toString).orElse(null);
        String templateId = Optional.ofNullable(JSONPath.eval(contentObject, "$.__sgs_platform_request_context.trfTemplateId")).map(Object::toString).orElse(null);
        String templateType = Optional.ofNullable(JSONPath.eval(contentObject, "$.__sgs_platform_request_context.trfTemplateType")).map(Object::toString).orElse(null);
        String formId = Optional.ofNullable(JSONPath.eval(contentObject, "$.__sgs_platform_request_context.formId")).map(Object::toString).orElse(null);
        String gridId = Optional.ofNullable(JSONPath.eval(contentObject, "$.__sgs_platform_request_context.gridId")).map(Object::toString).orElse(null);
        String labContactName = Optional.ofNullable(JSONPath.eval(contentObject, "$.__sgs_platform_request_context.labContactName")).map(Object::toString).orElse(null);
        TrfLabContactDTO labContact = Optional.ofNullable(JSONPath.eval(contentObject, "$.__sgs_platform_request_context.labContact"))
                .filter(json -> (json instanceof JSONObject))
                .map(json -> (JSONObject) json)
                .map(obj -> JSON.parseObject(obj.toJSONString(), TrfLabContactDTO.class))
                .orElse(null);
        TrfDTO trfDTO = customerTrfDomainService.convert(content, refSystemId, systemId, labCode, buCode, templateId, templateType, formId, gridId, labContact);
        afterConvertProcessor.processDffCodeMapping(refSystemId, formId, trfDTO);
        return trfDTO;
    }

}
