package com.sgs.customerbiz.biz.convert.impl.fn.unstable.tic;

import com.sgs.customerbiz.biz.convert.impl.fn.base.UnstableConvertFn;
import com.sgs.customerbiz.biz.convert.impl.fn.base.StringOperationFn;
import com.sgs.framework.model.enums.RefSystemIdEnum;
import com.sgs.framework.tool.utils.ObjectUtil;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * @author: shawn.yang
 * @create: 2023-06-12 15:03
 */
@Component
public class DFFFormDisplayFn extends StringOperationFn implements UnstableConvertFn {
    private static final Map<String,Map<String,String>> DFF_DISPLAY_MAP = new HashMap<>();

    static {
        Map<String,String> TIC_SL_DEFAULT_MAP = new HashMap<>();
        TIC_SL_DEFAULT_MAP.put("sampleName","Product Name");
        TIC_SL_DEFAULT_MAP.put("brandName","Brand");
        TIC_SL_DEFAULT_MAP.put("styleNo","Style No.");
        TIC_SL_DEFAULT_MAP.put("productGrade","Product Grade");
        TIC_SL_DEFAULT_MAP.put("safetyTechnicalLevel","Safety Category");
        TIC_SL_DEFAULT_MAP.put("manufacturerName","Manufacturer Name");
        TIC_SL_DEFAULT_MAP.put("specialInfo","Special information");
        Map<String,String> TIC_SL_HZ_WB_REGULAR_MAP = new HashMap<>();
        TIC_SL_HZ_WB_REGULAR_MAP.put("buyer","Buyer");
        TIC_SL_HZ_WB_REGULAR_MAP.put("sampleDescription","Sample Description");
        TIC_SL_HZ_WB_REGULAR_MAP.put("styleNo","Style No.");
        TIC_SL_HZ_WB_REGULAR_MAP.put("orderNo","Order No.");
        TIC_SL_HZ_WB_REGULAR_MAP.put("manufacturerName","Manufacturer");
        TIC_SL_HZ_WB_REGULAR_MAP.put("others","Other Info");

        DFF_DISPLAY_MAP.put(toKey(RefSystemIdEnum.TIC.getRefSystemId(),"SL-DEFAULT"),TIC_SL_DEFAULT_MAP);
        DFF_DISPLAY_MAP.put(toKey(RefSystemIdEnum.TIC.getRefSystemId(),"SL-HZ-WB-REGULAR"),TIC_SL_HZ_WB_REGULAR_MAP);


    }

    @Override
    public Object invoke(Object[] args) {
        int refSystemId = Integer.parseInt(args[0].toString());
        if (ObjectUtil.isEmpty(args[1]) || ObjectUtil.isEmpty(args[2])) {
            return null;
        }
        String dffName = args[1].toString();
        String dffKey = args[2].toString();
        Map<String, String> map = DFF_DISPLAY_MAP.get(toKey(refSystemId, dffName));
        return map == null ? null : map.get(dffKey);
    }

    private static String toKey(int refSystemId,String dffName){
        return refSystemId + "-" + dffName;
    }

    @Override
    public String getName() {

        return "dffFormDisplay";
    }

    @Override
    public String desc() {
        return null;
    }
}
