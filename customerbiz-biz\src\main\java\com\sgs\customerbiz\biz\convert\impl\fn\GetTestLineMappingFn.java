package com.sgs.customerbiz.biz.convert.impl.fn;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.sgs.customerbiz.biz.convert.impl.fn.base.UnstableConvertFn;
import com.sgs.customerbiz.integration.LocalILayerClient;
import com.sgs.customerbiz.integration.dto.TestLineMappingInfoDTO;
import com.sgs.customerbiz.model.trf.dto.TrfTestLineDTO;
import com.sgs.extsystem.facade.model.customer.req.CheckTestLineMappingReq;
import com.sgs.extsystem.facade.model.customer.req.MappingTestLineReq;
import com.sgs.framework.core.base.CustomResult;
import com.sgs.framework.tool.utils.Func;
import org.apache.commons.collections4.CollectionUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Component
public class GetTestLineMappingFn implements UnstableConvertFn {

    private final LocalILayerClient iLayerClient;

    public GetTestLineMappingFn(LocalILayerClient iLayerClient) {
        this.iLayerClient = iLayerClient;
    }

    @Override
    public Object invoke(Object[] args) {

        if(args == null || args.length != 3 || Stream.of(args).anyMatch(Objects::isNull)) {
            return Collections.emptyMap();
        }
        String customerGroupCode = args[0].toString();
        String productLineCode = args[1].toString();
        List<TrfTestLineDTO> testLineList = JSON.parseObject(JSON.toJSONString(args[2]), new TypeReference<List<TrfTestLineDTO>>() {
        });

        CheckTestLineMappingReq req = new CheckTestLineMappingReq();
        req.setCustomerGroupCode(customerGroupCode);
        req.setProductLineCode(productLineCode);
        req.setTestLines(CollectionUtils.isNotEmpty(testLineList)
                ? testLineList.stream().map(GetTestLineMappingFn::toTestLineMappingRequest).collect(Collectors.toList())
                : Collections.emptyList());

        CustomResult<List<TestLineMappingInfoDTO>> customResult = iLayerClient.queryTestLineMappingExists(req, TestLineMappingInfoDTO.class);
        if (Func.isEmpty(customResult) || CollectionUtils.isEmpty(customResult.getData())) {
            return Collections.emptyMap();
        }

        List<TestLineMappingInfoDTO> checkTestLineMappingRspList = customResult.getData();

        return checkTestLineMappingRspList.stream()
                .filter(info -> Objects.nonNull(info) && Objects.nonNull(info.getCondition()))
                .filter(info -> info.getCondition().stream().allMatch(c -> Objects.nonNull(c.getConditionId()) && Objects.nonNull(c.getConditionType())))
                .collect(
                        Collectors.toMap(
                                mapping -> mapping.getTestLineId().toString(),
                                info -> Optional.ofNullable(info.getCondition())
                                        .map(con -> con.stream()
                                                .collect(
                                                        Collectors.toMap(
                                                                TestLineMappingInfoDTO.Condition::getConditionId,
                                                                TestLineMappingInfoDTO.Condition::getConditionType,
                                                                (v1,v2) -> v1
                                                        )
                                                )
                                        )
                                        .orElse(Collections.emptyMap()),
                                (v1,v2) -> v1
                        )
                );
    }

    private static @NotNull MappingTestLineReq toTestLineMappingRequest(TrfTestLineDTO testLine) {
        MappingTestLineReq testLineReq = new MappingTestLineReq();
        testLineReq.setPpNo(testLine.getPpNo());
        testLineReq.setTestLineId(testLine.getTestLineId());
        return testLineReq;
    }

    @Override
    public String getName() {
        return "getTestLineMapping";
    }

    @Override
    public String desc() {
        return "getTestLineMapping";
    }
}
