package com.sgs.customerbiz.biz.convert.impl.fn;

import com.sgs.customerbiz.biz.convert.impl.fn.base.StringOperationFn;
import com.sgs.framework.tool.utils.Func;
import org.springframework.stereotype.Component;

import java.util.regex.Pattern;


@Component
public class ReplaceStringByPatternFn extends StringOperationFn {

    @Override
    public Object invoke(Object input, Object charsToReplace) {
        if (Func.isEmpty(input)) {
            return null;
        }
        if (Func.isEmpty(charsToReplace)) {
            return input;
        }
        String regex = "[" + Pattern.quote(charsToReplace.toString()) + "]";
        String value = subtractQuote(input.toString());
        return value.replaceAll(regex, "");
    }

    @Override
    public String getName() {
        return "replaceStrByPatternFn";
    }

    @Override
    public String desc() {
        return "功能：字符串替换;" +
                "接收两个参数:1.被要替换的值 2.需要替换的值";
    }
}
