package com.sgs.customerbiz.biz.convert.impl.fn;

import com.sgs.customerbiz.biz.convert.impl.fn.base.AbstractDataConvertFn;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Objects;

@Component
public class WhenNotNullFn extends AbstractDataConvertFn {

    @Override
    protected Object invoke(Object arg1, Object arg2, Object arg3) {
        boolean notNull = Objects.nonNull(arg1) && StringUtils.isNotBlank(arg1.toString());
        return notNull ? arg2: arg3;
    }

    @Override
    public String getName() {
        return "whenNotNull";
    }

    @Override
    public String desc() {
        return "whenNotNullFn";
    }
}
