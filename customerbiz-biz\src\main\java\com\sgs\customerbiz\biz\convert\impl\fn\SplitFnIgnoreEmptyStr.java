package com.sgs.customerbiz.biz.convert.impl.fn;

import com.sgs.customerbiz.biz.convert.impl.fn.base.StringOperationFn;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.stream.Collectors;
import java.util.stream.Stream;

@Component
public class SplitFnIgnoreEmptyStr extends StringOperationFn {
    @Override
    public Object invoke(Object[] args) {
        if (args == null || args.length == 0 || args[0] == null) {
            return null;
        }

        // 默认分隔符为,
        String delimiter = ",";
        String source = args[0].toString();
        if (args.length == 2) {
            // 自定义分隔符
            delimiter = args[1].toString();
        }

        return Stream.of(source.split(delimiter)).filter(StringUtils::isNotBlank).collect(Collectors.toList());
    }

    @Override
    public String getName() {
        return "splitIgnoreEmptyStr";
    }

    @Override
    public String desc() {
        return "将不为空的字符串按指定分隔符切割成字符集合";
    }
}

