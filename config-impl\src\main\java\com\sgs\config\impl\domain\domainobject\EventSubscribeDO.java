package com.sgs.config.impl.domain.domainobject;

import lombok.Data;

/**
 * @author: shawn.yang
 * @create: 2023-05-08 17:03
 */
@Data
public class EventSubscribeDO {

    private Long id;

    /**
     *  订阅者
     */
    private Integer subscriber;


    /**
     * 系统标识
     */
    private Integer refSystemId;

    /**
     * 事件code
     */
    private Integer eventCode;

    /**
     * 对应触发api的id
     */
    private Long apiId;

    /**
     * 事件通知规则 -1:无限制 ，低位开始，第一位：多次发送标识，第二位：顺序发送标识，第三位：trf允许合并发送标识
     */
    private Integer notifyRule;

    /**
     * 事件触发时需要发生给客户的数据 - 1:不需要 ，低位开始，第一位：Invoice，第二位：quotation，第三位：。。。具体参见标准对象定义
     */
    private Integer notifyData;

    /**
     * 处理器的实例名
     */
    private String handlerName;

    /**
     * 处理失败时触发的动作
     */
    private String errorHandle;

    private Integer priority;

    private String condition;
    private String conditionParams;
}
