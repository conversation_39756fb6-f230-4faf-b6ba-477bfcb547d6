package com.sgs.customerbiz.biz.convert.impl.fn;

import com.sgs.customerbiz.biz.convert.impl.fn.base.StringOperationFn;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

@Component
public class SliceByStartsFn extends StringOperationFn {
    @Override
    public Object invoke(Object arg1, Object arg2) {
        if (arg1 == null || arg2 == null) {
            return null;
        }

        String sourceStr = String.valueOf(arg1);
        String pattern = String.valueOf(arg2);

        if (StringUtils.isEmpty(sourceStr) || StringUtils.isEmpty(pattern)) {
            return sourceStr;
        }

        String lowerSourceStr = sourceStr.toLowerCase();
        String lowerPattern = pattern.toLowerCase();
        int startIndex = 0;

        while (lowerSourceStr.startsWith(lowerPattern, startIndex)) {
            startIndex += pattern.length();
        }

        return startIndex > 0 ? sourceStr.substring(startIndex) : sourceStr;
    }

    @Override
    public String getName() {
        return "sliceByStr";
    }

    @Override
    public String desc() {
        return "Slice string by removing all case-insensitive matching patterns from the start of the string";
    }
}
