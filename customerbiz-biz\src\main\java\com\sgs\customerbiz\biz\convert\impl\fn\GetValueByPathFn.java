package com.sgs.customerbiz.biz.convert.impl.fn;

import com.alibaba.fastjson.JSONPath;
import com.sgs.customerbiz.biz.convert.impl.fn.base.StringOperationFn;
import com.sgs.framework.tool.utils.Func;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.Optional;

@Slf4j
@Component
public class GetValueByPathFn extends StringOperationFn {

    @Override
    public Object invoke(Object[] arg1) {
        if (Func.isEmpty(arg1) || Func.isEmpty(arg1[0]) ||  arg1.length != 2) {
            return null;
        }
        return Optional.ofNullable(JSONPath.eval(arg1[0], "$." + arg1[1].toString())).map(Object::toString).orElse("");
    }
    @Override
    public String getName() {
        return "getValueByPathFn";
    }

    @Override
    public String desc() {
        return "getValueByPathFn";
    }
}
