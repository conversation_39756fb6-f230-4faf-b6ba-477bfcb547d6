package com.sgs.config.impl.service.impl;

import com.sgs.config.api.dto.EventSubscribeDTO;
import com.sgs.config.api.service.EventSubscribeService;
import com.sgs.config.impl.convertor.EventSubscribeConvertor;
import com.sgs.config.impl.domain.EventSubscribeDomainService;
import com.sgs.config.impl.domain.domainobject.EventSubscribeDO;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

/**
 * @author: shawn.yang
 * @create: 2023-05-08 17:16
 */
@Service
public class EventSubscribeServiceImpl implements EventSubscribeService {
    private final EventSubscribeDomainService eventSubscribeDomainService;

    @Override
    public List<EventSubscribeDTO> findBySubsciberAndRefSystemId(Integer subscriber, Integer refSystemId) {
        List<EventSubscribeDO> subscribeDOList = eventSubscribeDomainService.findBySubsciberAndRefSystemId(subscriber, refSystemId);
        return EventSubscribeConvertor.convertToDTO(subscribeDOList);
    }

    @Override
    public List<EventSubscribeDTO> findByRefSystemIdAndEventCode(Integer refSystemId, Integer eventCode) {
        List<EventSubscribeDO> eventSubscribeDOList = eventSubscribeDomainService.get(refSystemId, eventCode);
        return EventSubscribeConvertor.convertToDTO(eventSubscribeDOList);
    }

    @Override
    public List<EventSubscribeDTO> get(Integer refSystemId, Integer subscriber, Integer eventCode) {
        List<EventSubscribeDO> eventSubscribeDOS = eventSubscribeDomainService.get(refSystemId, subscriber, eventCode);
        return EventSubscribeConvertor.convertToDTO(eventSubscribeDOS);
    }

    @Override
    public List<EventSubscribeDTO> findBySubscriberAndEventCode(Optional<Long> apiId, Integer subscriber, Integer eventCode) {
        List<EventSubscribeDO> eventSubscribeDOList = eventSubscribeDomainService.getBySubscriber(apiId, subscriber, eventCode);
        return EventSubscribeConvertor.convertToDTO(eventSubscribeDOList);
    }


    public EventSubscribeServiceImpl(EventSubscribeDomainService eventSubscribeDomainService) {
        this.eventSubscribeDomainService = eventSubscribeDomainService;
    }
}
