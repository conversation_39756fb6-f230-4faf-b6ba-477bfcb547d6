package com.sgs.config.impl.domain.domainobject;

import lombok.Data;

@Data
public class ConfigInfoDO {
    /**
     * id BIGINT(19) 必填<br>
     * 主键
     */
    private Long id;

    /**
     * csutomer_group VARCHAR(32) 必填<br>
     * 配置方分组
     */
    private String csutomerGroup;

    /**
     * customer_no VARCHAR(32) 必填<br>
     * 配置方编号
     */
    private String customerNo;

    /**
     * identity_id VARCHAR(32) 必填<br>
     * 配置方身份标识
     */
    private String identityId;

    /**
     * product_line VARCHAR(20)<br>
     * BU
     */
    private String productLine;

    /**
     * config_type INTEGER(10) 必填<br>
     * 配置参数类型 1.技术参数配置 2.业务参数配置
     */
    private Integer configType;

    /**
     * config_key VARCHAR(200) 必填<br>
     * 配置key,如：ORDER_TRF_REL、TRF_CHANGE_MODE、TRF_UPDATE_LIMIT
     */
    private String configKey;

    /**
     * config_value VARCHAR(500) 必填<br>
     * 配置值
     */
    private String configValue;
}