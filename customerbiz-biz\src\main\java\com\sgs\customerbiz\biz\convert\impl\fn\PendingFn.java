package com.sgs.customerbiz.biz.convert.impl.fn;

import com.sgs.customerbiz.biz.convert.impl.fn.base.AbstractDataConvertFn;
import com.sgs.customerbiz.core.constants.Constants;
import com.sgs.customerbiz.domain.domainevent.*;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * @author: shawn.yang
 * @create: 2023-05-23 10:03
 */
@Component
public class PendingFn extends AbstractDataConvertFn {
    private static final Map<String, Integer> EVENT_ACTION_MAP = new HashMap<>();

    static {
        // =============== 初始化EVENT 和 Action mapping ===============
        EVENT_ACTION_MAP.put(TrfPendingEvent.class.getName(), Constants.ONE);
        EVENT_ACTION_MAP.put(TrfUnPendingEvent.class.getName(), Constants.ZERO);
        // ===========================================================
    }

    @Override
    public Object invoke(Object[] args) {
        if (args == null || args.length == 0) {
            return null;
        }
        Object arg0 = args[0];

        if (arg0 == null) {
            throw new NullPointerException("pendingFn args error");
        }
        Integer actionValue = EVENT_ACTION_MAP.get(arg0.toString());
        return actionValue;
    }


    @Override
    public String getName() {
        return "pendingFn";
    }

    @Override
    public String desc() {
        return "pendingFn转换。" +
                "接收1个参数：" +
                "1、eventName" +
                "返回：SyncPending：1；SyncUnPending：0";
    }
}
