package com.sgs.customerbiz.biz.convert.impl.fn;

import com.sgs.customerbiz.biz.convert.impl.fn.base.AbstractDataConvertFn;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * @author: shawn.yang
 * @create: 2023-06-06 20:23
 */
@Component
public class ListToStringFn extends AbstractDataConvertFn {

    @Override
    protected Object invoke(Object arg) {
        if (arg == null) {
            return null;
        }
        if (!(arg instanceof List)) {
            return arg.toString();
        }

        List<?> list = (List<?>) arg;
        return list.stream().map(Object::toString).collect(Collectors.joining(","));
    }

    @Override
    public String getName() {
        return "listToStr";
    }

    @Override
    public String desc() {
        return "把一个list转成字符串,使用逗号分割";
    }
}
