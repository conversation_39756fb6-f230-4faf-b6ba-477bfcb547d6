package com.sgs.customerbiz.web.aspect;

import com.alibaba.fastjson.JSONPath;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.ImmutableSet;
import com.google.common.collect.Maps;
import com.sgs.customerbiz.core.common.SciRequestContext;
import com.sgs.customerbiz.core.config.ProductLineContextHolder;
import com.sgs.customerbiz.core.constants.Constants;
import com.sgs.customerbiz.core.util.HeaderHelper;
import com.sgs.customerbiz.core.util.OptionalUtils;
import com.sgs.customerbiz.dbstorages.mybatis.mapper.todolist.ApiRequestMapper;
import com.sgs.customerbiz.dbstorages.mybatis.model.ApiRequestPO;
import com.sgs.customerbiz.facade.model.todolist.req.ApiRecord;
import com.sgs.customerbiz.infrastructure.api.IdService;
import com.sgs.customerbiz.model.trf.dto.req.SciBaseRequest;
import com.sgs.customerbiz.web.config.ApiRequestRecorderConfig;
import com.sgs.framework.core.base.BaseProductLine;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.core.base.ResponseCode;
import com.sgs.framework.core.exception.BizException;
import com.sgs.framework.model.enums.SgsSystem;
import com.sgs.framework.tool.utils.DateUtil;
import com.sgs.framework.tool.utils.Func;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import javax.servlet.http.HttpServletRequest;
import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Slf4j
@Aspect
@Component
// @Order(3) 确保在SCIAntiReplayAttackAspect后执行
@Order(3)
public class ApiRequestRecorderAspect {

    private final IdService idService;

    private final ObjectMapper objectMapper;

    private final ApiRequestMapper apiRequestMapper;

    private final ApiRequestRecorderConfig apiRequestRecorderConfig;

    private final BlockingQueue<Optional<ApiRequestPO>> queue;

    private final ExecutorService consumerExecutor;

    private static final String SYSTEM_ID = "systemId";

    @Value("${api.request.queue.max-size:500}")
    private int maxSizeOfQueue = 500;

    @Value("${api.request.queue.seconds-of-shutdown:60}")
    private int secondsOfShutdown = 60;

    private static final Set<Class<?>> wrapperClasses = ImmutableSet.<Class<?>>builder()
            .add(
                    Integer.class,
                    Long.class,
                    Double.class,
                    Float.class,
                    Character.class,
                    Byte.class,
                    Short.class,
                    Boolean.class,
                    String.class
            )
            .build();

    public ApiRequestRecorderAspect(IdService idService, ObjectMapper objectMapper, ApiRequestMapper apiRequestMapper, ApiRequestRecorderConfig apiRequestRecorderConfig) {
        this.idService = idService;
        this.objectMapper = objectMapper;
        this.apiRequestMapper = apiRequestMapper;
        this.apiRequestRecorderConfig = apiRequestRecorderConfig;
        this.consumerExecutor = Executors.newSingleThreadExecutor();
        this.queue = new LinkedBlockingQueue<>(maxSizeOfQueue);
    }

    @PostConstruct
    public void setUp() {
        this.consumerExecutor.execute(this::consume);
        log.info("apilog consumer of ApiRequestRecorderAspect is started");
    }

    @PreDestroy
    public void shutdown() {
        this.consumerExecutor.shutdownNow();
        try {
            if (!consumerExecutor.awaitTermination(secondsOfShutdown, TimeUnit.SECONDS)) {
                log.warn("Consumer executor did not terminate");
            }
            List<Optional<ApiRequestPO>> resetApiLogs = new ArrayList<>();
            queue.drainTo(resetApiLogs);
            for (Optional<ApiRequestPO> apiLog : resetApiLogs) {
                log.info("write apilog after shutdown threadpool");
                safeWriteIfPresent(apiLog);
            }
        } catch (InterruptedException ex) {
            consumerExecutor.shutdownNow();
            log.warn("Consumer executor terminated now");
            Thread.currentThread().interrupt();
        } finally {
            log.info("apilog consumer of ApiRequestRecorderAspect is shutdown");
        }
    }

    @Pointcut("execution(public * com.sgs.sci.common.service.controller.FileController.*(..))")
    public void sciCommonServicePointcut() {
    }

    @Pointcut(value = "execution(public * com.sgs.customerbiz.web.controllers..*Controller.*(..)))")
    public void apiLog(){}

    @Pointcut("execution(* com.sgs.customerbiz.web.controllers.HealthController.*(..))")
    public void excludeHealthController() {}

    @Around("(apiLog() || sciCommonServicePointcut()) && ! excludeHealthController()") // 定义切点表达式
    public Object aroundAdvice(ProceedingJoinPoint joinPoint) throws Throwable {
        Optional<ApiRequestPO> apiLog = prepareLog(joinPoint);
        try {
            Object resp = joinPoint.proceed();
            recordSuccess(apiLog, resp);
            return resp;
        }  catch (BizException ex) {
            recordException(apiLog, ex);
            throw ex;
        } catch (Throwable ex) {
            recordException(apiLog, ex);
            throw ex;
        } finally {
            queue.put(apiLog);
        }
    }

    private void recordSuccess(Optional<ApiRequestPO> apiLog, Object resp) {
        try {
            if(apiLog.isPresent()) {
                ApiRequestPO apiRequestPO = apiLog.get();
                int statusCode = 0;
                if (resp instanceof BaseResponse) {
                    statusCode = ((BaseResponse<?>) resp).getStatus();
                }
                setStatusAndBody(apiRequestPO, resp, statusCode);
            }
        } catch (Exception e) {
            //ignore
            log.warn("error occur when save api request", e);
        }
    }

    private void recordException(Optional<ApiRequestPO> apiLog, Throwable ex) {
        try {
            if(apiLog.isPresent()) {
                ApiRequestPO apiRequestPO = apiLog.get();
                setStatusAndBody(apiRequestPO,
                        new BaseResponse<>(ResponseCode.UNKNOWN.getCode(), ex.getMessage()),
                        ResponseCode.UNKNOWN.getCode()
                );
            }
        } catch (Exception e) {
            //ignore
            log.warn("error occur when save api request", e);
        }
    }

    private void recordException(Optional<ApiRequestPO> apiLog, BizException ex) {
        try {
            if(apiLog.isPresent()) {
                String errorMsg = ex.getMessage();
                ApiRequestPO apiRequestPO = apiLog.get();
                setStatusAndBody(apiRequestPO, new BaseResponse<>(
                        ex.getCode(),
                        StringUtils.isNotBlank(errorMsg) ? errorMsg : "服务器出了点小差错，请稍后再试."
                ), ex.getCode());
            }
        } catch (Exception e) {
            //ignore
            log.warn("error occur when save api request", e);
        }
    }

    private void setStatusAndBody(ApiRequestPO apiRequestPO, Object body, int status) throws JsonProcessingException {
        if(status != 200) {
            apiRequestPO.setResponseBody(objectMapper.writeValueAsString(body));
        }
        apiRequestPO.setResponseStatus(Func.toStr(status));
    }
    private volatile boolean shouldRun = true;
    private void consume() {
        try {
            while (shouldRun) {
                Optional<ApiRequestPO> apiLog = queue.take();
                safeWriteIfPresent(apiLog);
            }
        } catch (InterruptedException e) {
            log.info("logWriter consumer of ApiRequestRecorderAspect is interrupted");
            Thread.currentThread().interrupt();
        }
    }
    // 添加方法来控制循环
    public void stopConsuming() {
        shouldRun = false;
    }

    private void safeWriteIfPresent(Optional<ApiRequestPO> apiLog) {
        try {
            apiLog.ifPresent(apiRequestMapper::insert);
        } catch (Throwable e) {
            //ignore
            log.warn("error occur when save api request", e);
        }
    }

    private Optional<ApiRequestPO> prepareLog(ProceedingJoinPoint joinPoint) {
        try {
            // Check if this method should be excluded from recording
            String methodName = joinPoint.getSignature().getName();
            String systemIdFromHeader = HeaderHelper.getParamValue(SYSTEM_ID);

            if (shouldExcludeFromRecording(methodName, systemIdFromHeader)) {
                return Optional.empty();
            }

            SciRequestContext requestContext = ProductLineContextHolder.retrieveSciRequestContext();
            ApiRequestPO apiRequestPO = new ApiRequestPO();
            apiRequestPO.setId(idService.nextId());
            apiRequestPO.setSystemId(systemId(requestContext));
            apiRequestPO.setLabCode(requestContext.getLabCode());
            apiRequestPO.setRequestId(
                    Optional.ofNullable(requestContext.getRequestId())
                            .orElse(UUID.randomUUID().toString())
            );
            apiRequestPO.setRequestHeader(objectMapper.writeValueAsString(requestHeader()));
            Object[] args = joinPoint.getArgs(); // 获取方法参数
            String actionName = "";
            String requestBody = fetchBody(args);
            for (Object arg : args) {
                if (arg instanceof SciBaseRequest) {
                    //记录日志，取请求参数中的extId
                    SciBaseRequest sciBaseRequest = (SciBaseRequest) arg;
                    actionName = Optional.ofNullable(JSONPath.eval(sciBaseRequest, "$.action"))
                            .map(Object::toString)
                            .filter(StringUtils::isNotBlank)
                            .map(action -> " - " + action)
                            .orElse("");
                    String extId = sciBaseRequest.getExtId();
                    try {
                        if (Func.isBlank(extId)) {
                            extId = Optional.ofNullable(JSONPath.eval(sciBaseRequest, "$.trfNo")).map(Object::toString).orElse(null);
                        }
                    } catch (Throwable t) {
                        //ignore
                    }
                    apiRequestPO.setExtId(extId);
                    apiRequestPO.setRefSystemId(sciBaseRequest.getRefSystemId());
                } else if (arg instanceof ApiRecord) {
                    ApiRecord apiRecord = (ApiRecord) arg;
                    apiRequestPO.setExtId(apiRecord.getExtId());
                } else if (arg instanceof JsonNode) {
                    JsonNode jsonNode = ((JsonNode) arg);
                    try {
                        String extId = Optional.ofNullable(jsonNode.get("trfNo")).map(JsonNode::asText).orElseGet(() ->
                                Optional.ofNullable(jsonNode.get("packageBarcode")).map(JsonNode::asText).orElse(null));
                        apiRequestPO.setExtId(extId);
                        Optional.ofNullable(jsonNode.get("refSystemId"))
                                .map(JsonNode::asText)
                                .map(Integer::parseInt)
                                .ifPresent(apiRequestPO::setRefSystemId);
                    } catch (Throwable t) {
                        //ignore
                    }
                } else {
                    try {
                        String extId = Optional.ofNullable(JSONPath.eval(arg, "$.trfNo")).map(Object::toString).orElseGet(() ->
                                Optional.ofNullable(JSONPath.eval(arg, "$.packageBarcode")).map(Object::toString).orElse(null));
                        apiRequestPO.setExtId(extId);
                        Optional.ofNullable(JSONPath.eval(arg, "$.refSystemId"))
                                .map(Object::toString)
                                .map(Integer::parseInt)
                                .ifPresent(apiRequestPO::setRefSystemId);
                    } catch (Throwable t) {
                        //ignore
                    }
                }
            }
            apiRequestPO.setRequestBody(requestBody);
            apiRequestPO.setMethodName(getServletPath() + actionName);
            apiRequestPO.setCreatedDate(DateUtil.now());
            apiRequestPO.setModifiedDate(DateUtil.now());
            apiRequestPO.setCreatedBy(Constants.USER_DEFAULT);
            apiRequestPO.setModifiedBy(Constants.USER_DEFAULT);
            return Optional.of(apiRequestPO);
        } catch (Throwable t) {
            return Optional.empty();
        }
    }

    private String fetchBody(Object[] args) throws JsonProcessingException {
        String body = "{}";
        Optional<Object> reqObject = Stream.of(args).filter(arg -> arg instanceof BaseProductLine).findAny();
        if (reqObject.isPresent()) {
            body = objectMapper.writeValueAsString(reqObject.get());
        } else {
            Optional<Object> firstNotPrimitiveArg = Stream.of(args)
                    .filter(this::isNotPrimitive)
                    .findFirst();
            if(firstNotPrimitiveArg.isPresent()) {
                body = objectMapper.writeValueAsString(firstNotPrimitiveArg.get());
            }
        }
        return body;
    }



    public boolean isNotPrimitive(Object arg) {
        Class<?> argClass = arg.getClass();
        return ! wrapperClasses.contains(argClass) && ! argClass.isPrimitive();
    }

    private Integer systemId(SciRequestContext requestContext) {
        return Optional.ofNullable(requestContext)
                .map(SciRequestContext::getSystemId)
                .orElseGet(() -> Optional.ofNullable(RequestContextHolder.getRequestAttributes())
                        .map(requestAttributes -> (ServletRequestAttributes) requestAttributes)
                        .map(ServletRequestAttributes::getRequest)
                        .map(request -> request.getHeader(Constants.PRODUCT_LINE_CODE))
                        .flatMap(buCode -> {
                            switch (buCode) {
                                case "SL":
                                    return Optional.of(SgsSystem.SODA);
                                case "HL":
                                    return Optional.of(SgsSystem.GPO);
                                default:
                                    return Optional.empty();
                            }
                        })
                        .map(SgsSystem::getSgsSystemId)
                        .orElse(0));
    }

    private String getServletPath() {
        RequestAttributes requestAttributes = RequestContextHolder.getRequestAttributes();
        ServletRequestAttributes sra = (ServletRequestAttributes)requestAttributes;
        if(sra == null) {
            return "";
        }
        HttpServletRequest request = sra.getRequest();
        return request.getServletPath();
    }

    private Map<String, String> requestHeader() {
        HashMap<String, String> requestHeaderMap = Maps.newHashMap();
        RequestAttributes requestAttributes = RequestContextHolder.getRequestAttributes();
        if (requestAttributes == null) {
            return requestHeaderMap;
        }
        ServletRequestAttributes sra = (ServletRequestAttributes) requestAttributes;
        HttpServletRequest request = sra.getRequest();
        if (Func.isEmpty(request)) {
            return requestHeaderMap;
        }
        Enumeration<String> headerNames = request.getHeaderNames();
        if (Func.isEmpty(headerNames)) {
            return requestHeaderMap;
        }
        while (headerNames.hasMoreElements()) {
            String headerName = headerNames.nextElement();
            String headerValue = request.getHeader(headerName);
            requestHeaderMap.put(headerName, headerValue);
        }
        return requestHeaderMap;

    }

    /**
     * Check if the method should be excluded from recording based on configuration
     * @param methodName the method name from joinPoint
     * @param systemIdFromHeader the system ID from request header
     * @return true if should be excluded, false otherwise
     */
    private boolean shouldExcludeFromRecording(String methodName, String systemIdFromHeader) {
        if (apiRequestRecorderConfig == null ||
            apiRequestRecorderConfig.getExclusionMethods() == null ||
            apiRequestRecorderConfig.getExclusionMethods().isEmpty()) {
            return false;
        }

        return apiRequestRecorderConfig.getExclusionMethods().stream()
                .anyMatch(exclusion -> {
                    boolean methodMatches = StringUtils.equals(methodName, exclusion.getMethodName());
                    boolean systemIdMatches = StringUtils.equals(systemIdFromHeader, exclusion.getSystemId());
                    return methodMatches && systemIdMatches;
                });
    }

}
