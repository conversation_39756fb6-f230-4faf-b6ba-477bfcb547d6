package com.sgs.customerbiz.biz.convert.impl.fn;

import com.sgs.customerbiz.biz.convert.impl.fn.base.StringOperationFn;
import com.sgs.customerbiz.integration.DffClient;
import com.sgs.framework.tool.utils.Func;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.stream.Collectors;


@Component
public class QueryTemplateNameFn extends StringOperationFn {

    @Autowired
    private DffClient dffClient;

    @Override
    public Object invoke(Object[] args) {
        if (args ==null || args.length ==0) {
            return null;
        }
        if (Func.isEmpty(args[0])) {
            return null;
        }

        return dffClient.queryTemplateName(args[0].toString());
    }

    @Override
    public String getName() {
        return "queryTemplateNameFn";
    }

    @Override
    public String desc() {
        return "查询模板名称";
    }
}
