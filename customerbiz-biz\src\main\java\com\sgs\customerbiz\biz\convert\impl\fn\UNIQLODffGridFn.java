package com.sgs.customerbiz.biz.convert.impl.fn;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ArrayUtil;
import com.alibaba.fastjson.JSONObject;
import com.sgs.customerbiz.biz.convert.impl.fn.base.StringOperationFn;
import com.sgs.framework.model.enums.LanguageType;
import com.sgs.framework.model.enums.SampleType;
import com.sgs.framework.tool.utils.Func;
import com.sgs.testdatabiz.facade.model.dto.rd.report.*;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;


@Component
public class UNIQLODffGridFn extends StringOperationFn {

    private static String labCode = "SpecialCustomerAttribute2";

    @Override
    public Object invoke(Object[] args) {
        if (ArrayUtil.isEmpty(args) || args.length<5) {
            return null;
        }
        Object key = args[0];
        if (Func.isEmpty(key)) {
            return null;
        }

        Object key2 = args[1];
        if (Func.isEmpty(key2)) {
            return null;
        }
        List<RdTestSampleDTO> testSampleDTOList = null;
        if (key instanceof List) {
            testSampleDTOList = JSONObject.parseArray(JSONObject.toJSONString(key), RdTestSampleDTO.class);
        }  else {
            RdTestSampleDTO testSampleDTO = JSONObject.parseObject(JSONObject.toJSONString(key), RdTestSampleDTO.class);
            testSampleDTOList = new ArrayList<>(Arrays.asList(testSampleDTO));
        }
        if (Func.isEmpty(testSampleDTOList)) {
            return null;
        }

        RdReportDTO report = new RdReportDTO();
        if (key2 instanceof List) {
            List list = (List) key2;
            report = JSONObject.parseObject(JSONObject.toJSONString(list.get(0)), RdReportDTO.class);
        } else {
            report = JSONObject.parseObject(JSONObject.toJSONString(key2), RdReportDTO.class);
        }

        if (Func.isEmpty(report)) {
            return null;
        }
        List<RdSampleDTO> sampleDTOList = report.getSampleList();

        Object key3 = args[2];
        if (Func.isEmpty(key3)) {
            return null;
        }
        Object key4 = args[3];
        if (Func.isEmpty(key4)) {
            return null;
        }

        String testSampleInstanceId = key3.toString();

        List<RdTestSampleDTO> testSampleDTO = testSampleDTOList.stream().filter(l -> Objects.equals(l.getTestSampleInstanceId(), testSampleInstanceId)).collect(Collectors.toList());
        if (Func.isEmpty(testSampleDTO)) {
            return null;
        }

        RdTestSampleDTO testSample = CollUtil.get(testSampleDTO, 0);
        if (Func.isEmpty(testSample)) {
            return null;
        }


        Object key5 = args[4];
        String strKey5 = key5.toString();
        if (Objects.equals(strKey5, "1")) {
            if (Objects.equals(SampleType.MixSample.getSampleType(), testSample.getTestSampleType())) {
                List<RdTestSampleGroupDTO> testSampleGroupList = testSample.getTestSampleGroupList();
                if (Func.isEmpty(testSampleGroupList)) {
                    return null;
                }
                StringBuilder sb = new StringBuilder();
                for (int i = 0; i < testSampleGroupList.size(); i++) {
                    String originalSampleInstanceId = getOriginalSampleInstanceId(testSampleDTOList, testSampleGroupList.get(i).getTestSampleInstanceId());
                    String value = getValue(sampleDTOList, originalSampleInstanceId, key4.toString());
                    if (Func.isNotBlank(value)) {
                        if (i == testSampleGroupList.size() - 1) {
                            sb.append(value);
                        } else {
                            sb.append(value + "/");
                        }
                    }
                }
                return sb.toString();

            } else {
                String originalSampleInstanceId = getOriginalSampleInstanceId(testSampleDTOList, testSampleInstanceId);
                return getValue(sampleDTOList, originalSampleInstanceId, key4.toString());
            }
        } else {
            String originalSampleInstanceId = getOriginalSampleInstanceId(testSampleDTOList, testSampleInstanceId);
            return getValue(sampleDTOList, originalSampleInstanceId, key4.toString());
        }
    }


    private String getValue(List<RdSampleDTO> sampleDTOList, String testSampleInstanceId ,String labelCode) {
        List<RdSampleDTO> collect = sampleDTOList.stream().filter(l -> Objects.equals(testSampleInstanceId, l.getTestSampleInstanceId())).collect(Collectors.toList());
        if (Func.isEmpty(collect)) {
            return null;
        }
        List<RdProductSampleAttrDTO> sampleAttrList = collect.get(0).getSampleAttrList();
        if (Func.isEmpty(sampleAttrList)) {
            return null;
        }

        List<RdProductSampleAttrDTO> rdProductSampleAttrDTOS = sampleAttrList.stream().filter(l -> Objects.equals(l.getLabelCode(), labelCode)).collect(Collectors.toList());
        if (Func.isEmpty(rdProductSampleAttrDTOS)) {
            return null;
        }
        RdProductSampleAttrDTO sampleAttrDTO = rdProductSampleAttrDTOS.get(0);
        List<RdAttrLanguageDTO> languageList = sampleAttrDTO.getLanguageList();
        if (Func.isEmpty(languageList)) {
            return null;
        }
        List<RdAttrLanguageDTO> languageEn = languageList.stream().filter(l -> Objects.equals(l.getLanguageId(), LanguageType.English.getLanguageId())).collect(Collectors.toList());
        String value = null;
        if (Func.isNotEmpty(languageEn)) {
            value = languageEn.get(0).getValue();
        } else {
            List<RdAttrLanguageDTO> languageCn = languageList.stream().filter(l -> Objects.equals(l.getLanguageId(), LanguageType.Chinese.getLanguageId())).collect(Collectors.toList());
            value = languageCn.get(0).getValue();
        }
//        if (Objects.equals(labelCode, labCode)) {
//            switch (value) {
//                case "1":
//                    value = "Finished Product";
//                    break;
//                case "2":
//                    value = "Fabric";
//                    break;
//                case "3":
//                    value = "Trim";
//                    break;
//            }
//        }

        return value;
    }

    private String getOriginalSampleInstanceId(List<RdTestSampleDTO> testSampleList, String testSampleInstanceId) {
        List<RdTestSampleDTO> list = testSampleList.stream().filter(l -> Objects.equals(l.getTestSampleInstanceId(), testSampleInstanceId)).filter(l -> Func.isNotEmpty(l.getParentTestSampleId())).collect(Collectors.toList());
        if (Func.isNotEmpty(list)) {
            return this.getOriginalSampleInstanceId(testSampleList, list.get(0).getParentTestSampleId());
        } else {
            return testSampleInstanceId;
        }
    }


    @Override
    public String getName() {
        return "uNIQLODffGridFn";
    }

    @Override
    public String desc() {
        return "uNIQLODffGridFn";
    }
}
