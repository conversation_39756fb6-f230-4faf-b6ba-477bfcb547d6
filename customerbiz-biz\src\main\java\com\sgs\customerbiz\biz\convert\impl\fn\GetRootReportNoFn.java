package com.sgs.customerbiz.biz.convert.impl.fn;

import com.sgs.customerbiz.biz.convert.impl.fn.base.UnstableConvertFn;
import com.sgs.customerbiz.dbstorages.mybatis.model.TrfReportPO;
import com.sgs.customerbiz.domain.domainservice.TrfReportDomainService;
import com.sgs.framework.tool.utils.Func;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/1/9 16:18
 */
@Slf4j
@Component
public class GetRootReportNoFn implements UnstableConvertFn {

    @Autowired
    private TrfReportDomainService trfReportDomainService;

    @Override
    public Object invoke(Object[] args) {
        if (Func.isEmpty(args)) {
            return null;
        }
        if (Func.isEmpty(args[0]) || Func.isEmpty(args[1]) || Func.isEmpty(args[2])) {
            return null;
        }
        return trfReportDomainService.getRootReportNo(args[0].toString(), args[1].toString());
    }

    @Override
    public String getName() {
        return "getRootReportNoFn";
    }

    @Override
    public String desc() {
        return "getRootReportNoFn";
    }
}
