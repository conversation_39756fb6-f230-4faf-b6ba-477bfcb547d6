package com.sgs.customerbiz.biz.convert.impl.fn.unstable.f21;

import com.alibaba.fastjson.JSONObject;
import com.sgs.customerbiz.biz.convert.impl.fn.base.StringOperationFn;
import com.sgs.customerbiz.model.trf.enums.ConclusionCodeType;
import com.sgs.framework.tool.utils.Func;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * <AUTHOR>
 * @Date 2024/4/2 19:38
 */
@Slf4j
@Component
public class MergeDataToListFn extends StringOperationFn {

    @Override
    public Object invoke(Object arg1,Object arg2) {
        if (Func.isEmpty(arg1) || Func.isEmpty(arg2)) {
            return null;
        }

        List<Map<String, Object>> list = new ArrayList<>();

        JSONObject jsonObject = JSONObject.parseObject(arg1.toString());
        String key = arg2.toString();
        Object listObj = jsonObject.get(key);
        if (listObj instanceof List) {
             ((List<?>) listObj).forEach(
                    l -> {
                        Map<String, Object> map = new HashMap<>();
                        map.put(key, l);
                        jsonObject.forEach(
                                (k,v) -> {
                                    if (!Objects.equals(k, key)) {
                                        map.put(k, v);
                                    }
                                }
                        );
                        list.add(map);
                    }
            );
        }

        return list;
    }

    @Override
    public Object invoke(Object arg1,Object key1,Object key2) {
        if (Func.isEmpty(arg1)) {
            return null;
        }

        List<Map<String, Object>> list = new ArrayList<>();

        JSONObject jsonObject = JSONObject.parseObject(arg1.toString());
        Object o1 = jsonObject.get(key1);
        Object o2 = jsonObject.get(key2);

        if (Func.isEmpty(o1) || Func.isEmpty(o2)) {
            return null;
        }

        if (o1 instanceof List && o2 instanceof List) {
            int size = 0;
            List o1List = (List) o1;
            List o2List = (List) o2;
            if (o1List.size() > o2List.size()) {
                size = o1List.size();
            } else {
                size = o2List.size();
            }
            for (int i = 0; i < size; i++) {
                Map<String, Object> map = new HashMap<>();
                map.put(key1.toString(), i < o1List.size() ? o1List.get(i) : null);
                map.put(key2.toString(), i < o2List.size() ? o2List.get(i) : null);
                list.add(map);
            }

        }

        return list;
    }


    @Override
    public String getName() {
        return "mergeDataToListFn";
    }

    @Override
    public String desc() {
        return "mergeDataToListFn";
    }
}
