package com.sgs.customerbiz.biz.convert.impl.fn;

import com.googlecode.aviator.script.AviatorBindings;
import com.sgs.customerbiz.biz.convert.impl.fn.base.StringOperationFn;
import com.sgs.customerbiz.validation.validator.ScriptEvaluator;
import com.sgs.customerbiz.validation.validator.TypeUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.script.Bindings;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

@Component
@Slf4j
public class MathFn extends StringOperationFn {

    private static final Integer ZERO = 0;

    @Override
    public Object invoke(Object[] args) {
        if (args == null || args.length != 2 || args[0] == null || args[1] == null) {
            return null;
        }
        try {
            int num = Integer.parseInt(args[0].toString());
            Map<String, Object> param = new HashMap<>();
            param.put("value", num);
            Bindings bindings = new AviatorBindings();
            bindings.putAll(param);
            Object rel = ScriptEvaluator.eval(args[1].toString(), bindings);
            return Objects.nonNull(rel) ? rel.toString() : null;
        } catch (Throwable t) {
            log.error("cannot convert " +args[0].toString()+ " to number.", t);
            return null;
        }

    }

    public static void main(String[] args) {
        Map<String, Object> param = new HashMap<>();
        param.put("productionStage", "B");
        Bindings bindings = new AviatorBindings();
        bindings.putAll(param);
        Object rel = ScriptEvaluator.eval("!include(seq.set('FPT','FPT Full Package','PPT'), productionStage)", bindings);
        System.out.println(rel);
    }

    @Override
    public String getName() {
        return "math";
    }

    @Override
    public String desc() {
        return "math";
    }
}
