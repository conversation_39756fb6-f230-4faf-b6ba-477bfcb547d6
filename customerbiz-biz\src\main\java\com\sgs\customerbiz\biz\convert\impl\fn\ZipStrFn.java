package com.sgs.customerbiz.biz.convert.impl.fn;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.sgs.customerbiz.biz.convert.impl.fn.base.StringOperationFn;
import org.springframework.stereotype.Component;

import java.util.*;

@Component
public class ZipStrFn extends StringOperationFn {

    @Override
    protected Object invoke(Object arg1, Object arg2, Object arg3, Object arg4) {
        if(Objects.isNull(arg1) || Objects.isNull(arg2)) {
            return Collections.emptyList();
        }
        if(Objects.isNull(arg3) || Objects.isNull(arg4)) {
            throw new IllegalArgumentException("attrName is null");
        }
        String firstAttr = arg3.toString();
        String secondAttr = arg4.toString();
        JSONArray result = new JSONArray();
        if(arg1 instanceof List && arg2 instanceof List) {
            List<String> first = ((List<String>) arg1);
            List<String> second = ((List<String>) arg2);
            for (int i = 0; i < first.size(); i++) {
                JSONObject obj = new JSONObject();
                obj.put(firstAttr, first.get(i));
                if(i < second.size()) {
                    obj.put(secondAttr, second.get(i));
                }
                result.add(obj);
            }
        }

        return result;
    }

    @Override
    public String getName() {
        return "zipStr";
    }

    @Override
    public String desc() {
        return "zipStr";
    }
}
