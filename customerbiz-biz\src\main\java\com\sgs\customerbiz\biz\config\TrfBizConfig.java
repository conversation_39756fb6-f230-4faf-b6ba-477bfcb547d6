package com.sgs.customerbiz.biz.config;

import com.google.common.collect.Sets;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.Set;

/**
 * TRF系统配置
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "sci.biz.trf")
public class TrfBizConfig {

    /**
     * 需要特殊处理的systemId集合
     * 默认包含STARLIMS(36)和OTS(39)的systemId
     */
    private Set<Integer> systemIdSetOfUseTrfInfoRefSystemIds = Sets.newHashSet(36, 30);

} 