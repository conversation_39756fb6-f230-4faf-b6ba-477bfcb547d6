package com.sgs.config.api.dto.req;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Date;

@Getter
@AllArgsConstructor
public class DateRange {

    private Date startDateTime;
    private Date endDateTime;

    public static DateRange before(Date endDateTime) {
        return new DateRange(null, endDateTime);
    }

    public static DateRange range(Date startDateTime, Date endDateTime) {
        return new DateRange(startDateTime, endDateTime);
    }
}
