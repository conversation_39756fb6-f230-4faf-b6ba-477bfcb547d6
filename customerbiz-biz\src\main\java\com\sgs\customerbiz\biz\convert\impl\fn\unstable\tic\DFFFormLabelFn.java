package com.sgs.customerbiz.biz.convert.impl.fn.unstable.tic;

import com.sgs.customerbiz.biz.convert.impl.fn.base.UnstableConvertFn;
import com.sgs.customerbiz.biz.convert.impl.fn.base.StringOperationFn;
import com.sgs.framework.model.enums.RefSystemIdEnum;
import com.sgs.framework.tool.utils.ObjectUtil;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * @author: shawn.yang
 * @create: 2023-06-12 15:03
 */
@Component
public class DFFFormLabelFn extends StringOperationFn implements UnstableConvertFn {
    private static final Map<String, Map<String, String>> DFF_LABEL_MAP = new HashMap<>();

    static {
        Map<String, String> TIC_SL_DEFAULT_MAP = new HashMap<>();
        TIC_SL_DEFAULT_MAP.put("sampleName", "ProductDescription");
        TIC_SL_DEFAULT_MAP.put("brandName", "Collection");
        TIC_SL_DEFAULT_MAP.put("styleNo", "StyleNo");
        TIC_SL_DEFAULT_MAP.put("productGrade", "SpecialProductAttribute10");
        TIC_SL_DEFAULT_MAP.put("safetyTechnicalLevel", "SpecialProductAttribute9");
        TIC_SL_DEFAULT_MAP.put("manufacturerName", "FactoryName");
        TIC_SL_DEFAULT_MAP.put("specialInfo", "SpecialProductAttribute1");
        Map<String, String> TIC_SL_HZ_WB_REGULAR_MAP = new HashMap<>();
        TIC_SL_HZ_WB_REGULAR_MAP.put("buyer", "BuyerOrgannization1");
        TIC_SL_HZ_WB_REGULAR_MAP.put("sampleDescription", "ProductDescription");
        TIC_SL_HZ_WB_REGULAR_MAP.put("styleNo", "StyleNo");
        TIC_SL_HZ_WB_REGULAR_MAP.put("orderNo", "RefCode1");
        TIC_SL_HZ_WB_REGULAR_MAP.put("manufacturerName", "FactoryName");
        TIC_SL_HZ_WB_REGULAR_MAP.put("others", "SpecialProductAttribute2");

        DFF_LABEL_MAP.put(toKey(RefSystemIdEnum.TIC.getRefSystemId(), "SL-DEFAULT"), TIC_SL_DEFAULT_MAP);
        DFF_LABEL_MAP.put(toKey(RefSystemIdEnum.TIC.getRefSystemId(), "SL-HZ-WB-REGULAR"), TIC_SL_HZ_WB_REGULAR_MAP);


    }

    @Override
    public Object invoke(Object[] args) {
        int refSystemId = Integer.parseInt(args[0].toString());
        if (ObjectUtil.isEmpty(args[1]) || ObjectUtil.isEmpty(args[2])) {
            return null;
        }
        String dffName = args[1].toString();
        String dffKey = args[2].toString();
        Map<String, String> map = DFF_LABEL_MAP.get(toKey(refSystemId, dffName));
        return map == null ? null : map.get(dffKey);
    }

    private static String toKey(int refSystemId, String dffName) {
        return refSystemId + "-" + dffName;
    }

    @Override
    public String getName() {
        return "dffFormLabel";
    }

    @Override
    public String desc() {
        return null;
    }
}
