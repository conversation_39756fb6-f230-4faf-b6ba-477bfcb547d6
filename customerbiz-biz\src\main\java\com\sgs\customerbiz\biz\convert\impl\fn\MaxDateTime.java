package com.sgs.customerbiz.biz.convert.impl.fn;

import com.sgs.customerbiz.biz.convert.impl.fn.base.StringOperationFn;
import com.sgs.customerbiz.validation.validator.TypeUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Collection;
import java.util.Date;
import java.util.Objects;

@Component
@Slf4j
public class MaxDateTime extends StringOperationFn {

    @Override
    protected Object invoke(Object arg1) {
        if (!(arg1 instanceof Collection)) {
            log.warn("MaxDateTime function expects a Collection argument, but received: {}", arg1);
            return null;
        }

        Collection<?> collection = (Collection<?>) arg1;
        
        return collection.stream()
                .filter(Objects::nonNull)  // 过滤掉空值
                .filter(TypeUtils::canConvertToLocalDateTime)  // 过滤出可以转换为LocalDateTime的元素
                .map(obj -> {
                    try {
                        return TypeUtils.convertToLocalDateTime(obj);
                    } catch (Exception e) {
                        log.warn("Failed to convert object to LocalDateTime: {}", obj, e);
                        return null;
                    }
                })
                .filter(Objects::nonNull)  // 过滤掉转换失败的元素
                .max(LocalDateTime::compareTo)  // 找出最大的LocalDateTime
                .map(localDateTime -> Date.from(localDateTime.atZone(ZoneId.systemDefault()).toInstant()))  // 转换为Date
                .orElse(null);  // 如果没有有效的日期时间，返回null
    }

    @Override
    public String getName() {
        return "maxDateTime";
    }

    @Override
    public String desc() {
        return "Find the maximum date time from a list of date time values";
    }
}
