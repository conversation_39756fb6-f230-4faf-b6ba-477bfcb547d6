package com.sgs.config.api.dto;

import lombok.Data;

import java.util.Date;
import java.util.Map;

/**
 * @Desc
 * <AUTHOR>
 * @date 2024/1/9 14:30
 */
@Data
public class DFFMappingDTO {

    /**
     * id BIGINT(19) 必填<br>
     * ID唯一标识
     */
    private Long id;

    /**
     * bu_code VARCHAR(50)<br>
     * BU Code
     */
    private String buCode;

    /**
     * customer_group_code VARCHAR(50)<br>
     * 客户组代码
     */
    private String customerGroupCode;

    /**
     * customer_no VARCHAR(32)<br>
     * 客户编码
     */
    private String customerNo;

    /**
     * ref_system_id INTEGER(10)<br>
     * 客户系统编号
     */
    private Integer refSystemId;

    /**
     * type VARCHAR(32) 必填<br>
     * DFF 字段适用类型
     */
    private String type;

    /**
     * field_code VARCHAR(50) 必填<br>
     */
    private String fieldCode;

    /**
     * label_code VARCHAR(128) 必填<br>
     * 标准字段属性名
     */
    private String labelCode;

    /**
     * label_name VARCHAR(50) 必填<br>
     * 标准字段属性显示名称
     */
    private String labelName;

    /**
     * customer_label VARCHAR(128) 必填<br>
     * 客户侧字段属性显示名称
     */
    private String customerLabel;

    /**
     * customer_field VARCHAR(128) 必填<br>
     * 客户侧字段属性名
     */
    private String customerField;

    /**
     *  seq INTEGER(10) 必填<br>
     */
    private Integer seq;

    /**
     * active_indicator TINYINT(3) 必填<br>
     * 有效无效标记：0 - inactive， 1 - active
     */
    private Integer activeIndicator;

    /**
     * created_by VARCHAR(50) 默认值[system]<br>
     * 创建人
     */
    private String createdBy;

    /**
     * created_date TIMESTAMP(19) 默认值[CURRENT_TIMESTAMP]<br>
     * 创建时间
     */
    private Date createdDate;

    /**
     * modified_by VARCHAR(50) 默认值[system]<br>
     * 修改人
     */
    private String modifiedBy;

    /**
     * modified_date TIMESTAMP(19) 默认值[CURRENT_TIMESTAMP]<br>
     * 修改时间
     */
    private Date modifiedDate;

    /**
     * standard_dff CHAR(1) 默认值[Y]<br>
     * 是否标准字段，Y 通过dff code mapping转换，其它不转
     */
    private String standardDff;

    /**
     * value_mapping VARCHAR(500)<br>
     * 值的映射配置
     */
    private Map<String, String> valueMap;

}
