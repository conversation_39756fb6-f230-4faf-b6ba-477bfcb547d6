package com.sgs.customerbiz.biz.convert.impl.fn;

import com.alibaba.fastjson.JSONPath;
import com.sgs.customerbiz.biz.convert.impl.fn.base.StringOperationFn;
import com.sgs.customerbiz.biz.convert.impl.fn.base.UnstableConvertFn;
import com.sgs.framework.tool.utils.Func;
import org.springframework.stereotype.Component;

import java.util.*;

@Component
public class MapFn extends StringOperationFn implements UnstableConvertFn {
    @Override
    public Object invoke(Object[] args) {
        if (args.length != 2) {
            throw new IllegalArgumentException();
        }
        Object requestObject = args[0];
        Object keyObject = args[1];

        if (Func.isEmpty(requestObject)) {
            return null;
        }
        if (Func.isEmpty(keyObject)) {
            return null;
        }
        String path = keyObject.toString();

        Map<Object, List<Object>> map = new HashMap<>();
        if (requestObject instanceof Collection) {
            for (Object o : (Collection) requestObject) {
                Object key = JSONPath.eval(o, "$." + path);
                if (Func.isNotEmpty(key)) {
                    if (map.containsKey(key)) {
                        map.get(key).add(o);
                    } else {
                        map.put(key, new ArrayList<>(Collections.singletonList(o)));
                    }
                }
            }
        }
        return map;
    }

    @Override
    public String getName() {
        return "mapFn";
    }

    @Override
    public String desc() {
        return null;
    }
}
