package com.sgs.customerbiz.biz.convert.impl.fn;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.sgs.customerbiz.biz.convert.impl.fn.base.UnstableConvertFn;
import com.sgs.framework.core.exception.BizException;
import com.sgs.framework.tool.utils.Func;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2024/1/9 16:18
 */
@Slf4j
@Component
public class JoinValueByLanguageIdFn implements UnstableConvertFn {
    @Override
    public Object invoke(Object[] args) {
        if (args == null) {
            return null;
        }
        List<Object> argList = flattenNestedLists(args[0]);

        Object arg2 = args[1];
        Object arg3 = args[2];
        Object arg4 = args[3];
        String join = getJoinDelimiter(args);
        try {
            List<String> list = filterById(JSONObject.toJSONString(argList), Integer.parseInt(arg2.toString()), arg3.toString(), arg4.toString());
            if (Func.isEmpty(list)) {
                return null;
            }
            return String.join(join, list);

        } catch (IOException e) {
            throw new BizException(e.getMessage());
        }
    }

    private List<Object> flattenNestedLists(Object arg) {
        List<Object> flattenedList = new ArrayList<>();

        if (arg instanceof List) {
            List<?> argList = (List<?>) arg;

            for (Object o : argList) {
                if (o instanceof List) {
                    flattenedList.addAll(flattenNestedLists(o));
                } else {
                    flattenedList.add(o);
                }
            }
        }

        return flattenedList;
    }

    private String getJoinDelimiter(Object[] args) {
        String join = ",";

        if (args.length == 5 && Func.isNotEmpty(args[4])) {
            join = args[4].toString();
        }

        return join;
    }

    public static List<String> filterById(String jsonString, int targetId, String fieldName, String getFieldName) throws IOException {
        ObjectMapper mapper = new ObjectMapper();
        JsonNode root = mapper.readTree(jsonString);

        List<String> filteredNodes = new ArrayList<>();

        for (JsonNode node : root) {
            if (node.has(fieldName) && node.get(fieldName).asInt() == targetId) {
                filteredNodes.add(node.get(getFieldName).asText());
            }
        }

        return filteredNodes;
    }

    @Override
    public String getName() {
        return "joinValueByLanguageIdFn";
    }

    @Override
    public String desc() {
        return "joinValueByLanguageIdFn";
    }
}
