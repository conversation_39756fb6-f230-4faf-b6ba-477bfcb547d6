package com.sgs.customerbiz.biz.convert.impl.fn;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ArrayUtil;
import com.alibaba.fastjson.JSONObject;
import com.sgs.customerbiz.biz.convert.impl.fn.base.StringOperationFn;
import com.sgs.customerbiz.biz.utils.ILayerMappingUtil;
import com.sgs.customerbiz.model.ext.dto.req.MappingBasicDataReq;
import com.sgs.customerbiz.model.ext.dto.rsp.NewCheckTestLineMappingRsp;
import com.sgs.customerbiz.model.ext.dto.rsp.GetBasicDataMappingRsp;
import com.sgs.customerbiz.model.trf.dto.TrfAnalyteDTO;
import com.sgs.customerbiz.model.trf.dto.TrfTestLineDTO;
import com.sgs.framework.tool.utils.Func;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;


@Component
public class UNIQLOTestLineMappingFn extends StringOperationFn {

    private static final String UNIQLO_CUSTOMER_GROUP_CODE = "CG0000237";

    private static final String Analyte = "Analyte";

    private static final String DEFAULT_NINE = "999";

    private static final Integer REF_SYSTEM_ID = 10017;

    @Override
    public Object invoke(Object[] args) {
        if (ArrayUtil.isEmpty(args)) {
            return null;
        }
        Object key = args[0];
        if (Func.isEmpty(key)) {
            return null;
        }

        Object key2 = args[1];
        if (Func.isEmpty(key2)) {
            return null;
        }

        TrfTestLineDTO testLineDTO = JSONObject.parseObject(JSONObject.toJSONString(key), TrfTestLineDTO.class);

        List<NewCheckTestLineMappingRsp> testLineMappingReq = ILayerMappingUtil.getTestLineMappingReq(UNIQLO_CUSTOMER_GROUP_CODE, REF_SYSTEM_ID, Arrays.asList(testLineDTO));
        if (Func.isNotEmpty(testLineMappingReq)) {


            NewCheckTestLineMappingRsp checkTestLineMappingRsp = testLineMappingReq.get(0);
            switch (key2.toString()) {
                case "1":
                    return checkTestLineMappingRsp.getItemCode();
                case "2":
                    return Func.isNotEmpty(checkTestLineMappingRsp.getTestMethodCode()) ? checkTestLineMappingRsp.getTestMethodCode() : "999";
                case "3":

                    List<TrfAnalyteDTO> analyteList = testLineDTO.getAnalyteList();
                    if (Func.isEmpty(analyteList)) {
                        return DEFAULT_NINE;
                    }

                    if (Func.isEmpty(args[3])) {
                        return DEFAULT_NINE;
                    }
                    String analyteInstanceId = args[3].toString();
                    TrfAnalyteDTO trfAnalyteDTO = CollUtil.get(analyteList.stream().filter(l -> Objects.equals(l.getAnalyteInstanceId(), analyteInstanceId)).collect(Collectors.toList()), 0);
                    if (Func.isEmpty(trfAnalyteDTO)) {
                        return DEFAULT_NINE;
                    }

                    MappingBasicDataReq basicDataReq = new MappingBasicDataReq();
                    basicDataReq.setCustomerGroupCode(UNIQLO_CUSTOMER_GROUP_CODE);
                    basicDataReq.setProductLineCode(args[2].toString());
                    basicDataReq.setModuleType(Analyte);
                    basicDataReq.setFilters(Arrays.asList(trfAnalyteDTO.getAnalyteId().toString()));
                    List<GetBasicDataMappingRsp> basicDataMapping = ILayerMappingUtil.getBasicDataMapping(basicDataReq);
                    if (Func.isEmpty(basicDataMapping)) {
                        return DEFAULT_NINE;
                    }
                    return Func.isNotEmpty(basicDataMapping.get(0).getCustomer().getJugdementDivisionCode()) ? basicDataMapping.get(0).getCustomer().getJugdementDivisionCode() : "999";
            }
        }

        if (Objects.equals(key2.toString(), "2")) {
            return DEFAULT_NINE;
        }

        return null;
    }


    @Override
    public String getName() {
        return "uNIQLOTestLineMappingFn";
    }

    @Override
    public String desc() {
        return "uNIQLOTestLineMappingFn";
    }
}
