package com.sgs.config.api.dto.req;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.ImmutableList;
import lombok.Data;

import java.util.List;

@Data
public class RecipientConfigInfo {

    public enum RecipientType {
        Bus, CustomerInterface, System
    }

    private Integer refSystemId;
    private String labCode;
    private RecipientType type;
    private String customerName;
    private List<String> mailTo;

    public static void main(String[] args) {
        RecipientConfigInfo configInfo = new RecipientConfigInfo();
        configInfo.setLabCode("GZ");
        configInfo.setType(RecipientType.Bus);
        configInfo.setMailTo(ImmutableList.of("<EMAIL>"));
        System.out.println(JSON.toJSONString(configInfo));
    }

}
