package com.sgs.customerbiz.biz.convert.impl.fn;

import com.sgs.customerbiz.biz.convert.impl.fn.base.StringOperationFn;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Component
public class MergeToSetFn extends StringOperationFn {

    private static final Integer ZERO = 0;

    @Override
    public Object invoke(Object[] args) {
        if (args == null || args.length == 0) {
            return null;
        }
        return Stream.of(args)
                .filter(Objects::nonNull)
                .flatMap(arg -> {
                    if (arg instanceof List) {
                        return ((List<?>) arg).stream().map(this::filter);
                    }
                    return Stream.of(filter(arg));
                })
                .filter(Optional::isPresent)
                .map(Optional::get)
                .collect(Collectors.toSet());
    }

    private Optional<Object> filter(Object item) {
        if(Objects.isNull(item)) {
            return Optional.empty();
        }
        if(item instanceof String) {
            String itemStr = (String) item;
            return Optional.ofNullable(StringUtils.isNotBlank(itemStr) ? itemStr: null);
        }
        return Optional.of(item);
    }

    @Override
    public String getName() {
        return "mergeToSet";
    }

    @Override
    public String desc() {
        return "mergeToSetFn";
    }
}

