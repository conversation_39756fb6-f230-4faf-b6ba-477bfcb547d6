package com.sgs.customerbiz.biz.convert.impl.fn.base;

/**
 * @author: shawn.yang
 * @create: 2023-05-25 16:20
 */
public abstract class StringOperationFn extends AbstractDataConvertFn {

    protected final String subtractQuote(String value) {
        if (value.startsWith("\"") || value.startsWith("'")) {
            value = value.substring(1);
        }
        if (value.endsWith("\"") || value.endsWith("'")) {
            value = value.substring(0, value.length() - 1);
        }
        return value;
    }
}
