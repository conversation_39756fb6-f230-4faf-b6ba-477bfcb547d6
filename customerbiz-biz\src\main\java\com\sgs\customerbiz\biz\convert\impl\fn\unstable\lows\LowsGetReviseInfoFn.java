package com.sgs.customerbiz.biz.convert.impl.fn.unstable.lows;

import cn.hutool.core.date.DateUtil;
import com.sgs.customerbiz.biz.convert.impl.fn.base.UnstableConvertFn;
import com.sgs.customerbiz.core.util.DateFormatUtil;
import com.sgs.framework.tool.utils.Func;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * <AUTHOR>
 * @Date 2024/1/9 19:29
 */
@Slf4j
@Component
public class LowsGetReviseInfoFn implements UnstableConvertFn {
    private static final String revisedReport = "revisedReport";
    private static final String revisedDate = "revisedDate";

    private static final String DEFAULT_FORMAT = "MM/dd/yyyy";

    @Override
    public Object invoke(Object[] args) {
        if (Func.isEmpty(args)) {
            return null;
        }
        String arg1 = args[0].toString();
        Object arg2 = args[1];
        Object arg3 = args[2];
        if (Objects.equals(arg1, revisedReport)) {
            if (Func.isEmpty(arg2)) {
                return "N";
            } else {
                return "Y";
            }
        } else if (Objects.equals(arg1, revisedDate)) {
            if (Func.isNotEmpty(arg2) && Func.isNotEmpty(arg3)) {
                if (arg3 instanceof Long) {
                    return DateFormatUtil.formatDate(DEFAULT_FORMAT, (Long) arg3);
                } else {
                    return DateUtil.format(DateUtil.parse(arg3.toString()), DEFAULT_FORMAT);
                }
            }
        }
        return null;
    }

    @Override
    public String getName() {
        return "lowsGetReviseInfoFn";
    }

    @Override
    public String desc() {
        return "lowsGetReviseInfoFn";
    }
}
