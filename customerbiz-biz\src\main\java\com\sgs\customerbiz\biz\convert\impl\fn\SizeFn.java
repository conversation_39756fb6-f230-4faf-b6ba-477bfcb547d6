package com.sgs.customerbiz.biz.convert.impl.fn;

import com.sgs.customerbiz.biz.convert.impl.fn.base.UnstableConvertFn;
import com.sgs.framework.tool.utils.Func;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/1/9 19:18
 */
@Slf4j
@Component
public class SizeFn implements UnstableConvertFn {
    @Override
    public Object invoke(Object[] args) {
        if (Func.isEmpty(args)) {
            return null;
        }
        if (args[0] instanceof List) {
            return ((List<?>) args[0]).size();
        }
        return null;
    }

    @Override
    public String getName() {
        return "sizeFn";
    }

    @Override
    public String desc() {
        return "sizeFn";
    }
}
