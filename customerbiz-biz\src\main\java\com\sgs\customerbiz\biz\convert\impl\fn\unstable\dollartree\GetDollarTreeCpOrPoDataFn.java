package com.sgs.customerbiz.biz.convert.impl.fn.unstable.dollartree;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSONObject;
import com.sgs.customerbiz.biz.convert.impl.fn.base.StringOperationFn;
import com.sgs.customerbiz.integration.LocalILayerClient;
import com.sgs.customerbiz.integration.dto.CpResult;
import com.sgs.customerbiz.integration.dto.PoResult;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.tool.utils.Func;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;
import java.util.Objects;

@Slf4j
@Component
public class GetDollarTreeCpOrPoDataFn extends StringOperationFn {

    @Autowired
    private LocalILayerClient iLayerClient;

    @Override
    public Object invoke(Object[] arg1) {
        if (Func.isEmpty(arg1) || arg1.length != 4) {
            return null;
        }

        String key = arg1[0].toString();
        Integer refSystemId = Func.toInt(arg1[1]);
        if(Objects.isNull(arg1[2]) || StringUtils.isBlank(arg1[2].toString())) {
            return null;
        }
        String no = arg1[2].toString();
        String productLineCode = arg1[3].toString();

        switch (key) {
            case "1":
                CpResult cpResultBaseResponse = iLayerClient.queryCpData(refSystemId, no, productLineCode);
                if (Func.isNotEmpty(cpResultBaseResponse) ) {
                    return Func.isNotEmpty(cpResultBaseResponse) ? JSONObject.parseObject(JSONObject.toJSONString(cpResultBaseResponse)) : null;
                }
                return null;
            case "2":
                PoResult poResultBaseResponse = iLayerClient.queryPoData(refSystemId, no, productLineCode);
                if (Func.isNotEmpty(poResultBaseResponse) ) {
                    return Func.isNotEmpty(poResultBaseResponse) ? JSONObject.parseObject(JSONObject.toJSONString(poResultBaseResponse)) : null;
                }
                return null;
        }
        return null;

    }
    @Override
    public String getName() {
        return "getDollarTreeCpOrPoDataFn";
    }

    @Override
    public String desc() {
        return "getDollarTreeCpOrPoDataFn";
    }
}
