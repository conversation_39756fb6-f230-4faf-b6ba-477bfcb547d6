package com.sgs.customerbiz.biz.convert.impl.fn.unstable.lows;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.sgs.customerbiz.biz.convert.impl.fn.base.UnstableConvertFn;
import com.sgs.customerbiz.domain.domainservice.TrfDomainService;
import com.sgs.customerbiz.facade.model.req.QueryTestLineMappingReq;
import com.sgs.customerbiz.integration.ConfigClient;
import com.sgs.customerbiz.integration.LocalILayerClient;
import com.sgs.customerbiz.model.ext.dto.req.MappingBasicDataReq;
import com.sgs.customerbiz.model.ext.dto.req.MappingTestLineReq;
import com.sgs.customerbiz.model.ext.dto.req.NewCheckTestLineMappingReq;
import com.sgs.customerbiz.model.ext.dto.rsp.GetBasicDataMappingRsp;
import com.sgs.customerbiz.model.ext.dto.rsp.NewCheckTestLineMappingRsp;
import com.sgs.framework.core.base.CustomResult;
import com.sgs.framework.tool.utils.Func;
import com.sgs.testdatabiz.facade.model.dto.rd.report.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2024/1/9 16:18
 */
@Slf4j
@Component
public class GetResultValueByTypeFn implements UnstableConvertFn {

    @Autowired
    private LocalILayerClient localILayerClient;

    @Autowired
    private ConfigClient configClient;

    private static final String splitFlag = "=";

    private static final String LowesAnalyte = "LowesAnalyte";

    @Override
    public Object invoke(Object[] args) {
        if (Func.isEmpty(args) || args.length != 7) {
            return null;
        }
        Object reportObj = args[0];
        Object testResultListObj = args[1];
        Object testLineListObj = args[2];
        Object keyObj = args[3];
        Object valueObj = args[4];
        Object refSystemId = args[5];
        Object bu = args[6];

        List<RdTestResultDTO> testResultDTOList = new ArrayList<>();
        List<RdTestLineDTO> testLineDTOList = new ArrayList<>();

        RdReportDTO rdReportDTO = JSONObject.parseObject(reportObj.toString(), RdReportDTO.class);
        if (Func.isEmpty(rdReportDTO)) {
            return null;
        }

        List<RdReportMatrixDTO> reportMatrixList = rdReportDTO.getReportMatrixList();
        if (Func.isEmpty(reportMatrixList)) {
            return null;
        }

        Map<String, String> matrixMap = new HashMap<>();
        reportMatrixList.forEach(
                matrix -> matrixMap.put(matrix.getTestLineInstanceId(), matrix.getTestMatrixId())
        );


        if (testResultListObj instanceof List) {
            testResultDTOList = JSONArray.parseArray(testResultListObj.toString(), RdTestResultDTO.class);
        }

        Map<String, String> resultValueMap = new HashMap<>();
        testResultDTOList.forEach(
                testResult -> {
                    String testMatrixId = testResult.getTestMatrixId();
                    RdTestResultResultDTO result = testResult.getTestResult();
                    if (Func.isNotEmpty(result) && Func.isNotEmpty(result.getTestResultFullNameRel())) {
                        String resultValue = result.getResultValue();
                        resultValueMap.put(testMatrixId + splitFlag + result.getTestResultFullNameRel().getAnalyteInstanceId(), resultValue);
                    }
                }
        );

        if (testLineListObj instanceof List) {
            testLineDTOList = JSONArray.parseArray(testLineListObj.toString(), RdTestLineDTO.class);
        }
        Map<String, String> testLineMap = new HashMap<>();
        testLineDTOList.forEach(
                testLine -> {
                    Integer testLineId = testLine.getTestLineId();
                    List<RdPpTestLineRelDTO> ppTestLineRelList = testLine.getPpTestLineRelList();
                    List<RdAnalyteDTO> analyteList = testLine.getAnalyteList();
                    if (Func.isNotEmpty(ppTestLineRelList) && Func.isNotEmpty(analyteList)) {
                        ppTestLineRelList.forEach(
                                pp -> {
                                    Integer ppNo = pp.getPpNo();
                                    analyteList.forEach(
                                            analyte -> {
                                                Integer analyteId = analyte.getAnalyteId();
                                                testLineMap.put(testLineId + splitFlag + ppNo + splitFlag + analyteId, testLine.getTestLineInstanceId() + splitFlag + analyte.getAnalyteInstanceId());
                                            }
                                    );
                                }
                        );
                    }
                }
        );


        String fullIds = this.getResultValue(Integer.parseInt(refSystemId.toString()), keyObj.toString(), valueObj.toString(), bu.toString());
        if (Func.isEmpty(fullIds)) {
            return null;
        }

        String[] split = fullIds.split(splitFlag);
        if (Func.isEmpty(split) || split.length != 3) {
            return null;
        }

        String testLineId = split[0];
        String ppNo = split[1];
        String analyteId = split[2];

        String fullValue = testLineMap.get(testLineId + splitFlag + ppNo + splitFlag + analyteId);
        if (Func.isBlank(fullValue)) {
            return null;
        }

        String[] splitValues = fullValue.split(splitFlag);
        String testLineInstanceId = splitValues[0];
        String analyteInstanceId = splitValues[1];
        String testMatrixId = matrixMap.get(testLineInstanceId);

        String resultValue = resultValueMap.get(testMatrixId + splitFlag + analyteInstanceId);

        return resultValue;
    }


    private String getResultValue(Integer refSystemId, String key, String value, String bu) {
        List<String> customerGroupCodeByRefSystemId = configClient.getCustomerGroupCodeByRefSystemId(refSystemId);
        String customerGroupCode = customerGroupCodeByRefSystemId.get(0);

        NewCheckTestLineMappingReq testLineMappingReq = new NewCheckTestLineMappingReq();
        testLineMappingReq.setCustomerGroupCode(customerGroupCode);
        testLineMappingReq.setRefSystemId(refSystemId);
        testLineMappingReq.setProductLineCode(bu);

        MappingTestLineReq mappingTestLineReq = new MappingTestLineReq();
        mappingTestLineReq.setItemName(key);
        testLineMappingReq.setTestLines(Arrays.asList(mappingTestLineReq));

        CustomResult<List<NewCheckTestLineMappingRsp>> listCustomResult = localILayerClient.checkTestLineMappingExists(testLineMappingReq);
        if (!listCustomResult.isSuccess()) {
            return null;
        }
        List<NewCheckTestLineMappingRsp> data = listCustomResult.getData();
        if (Func.isEmpty(data)) {
            return null;
        }
        NewCheckTestLineMappingRsp newCheckTestLineMappingRsp = data.get(0);

        Integer ppNo = newCheckTestLineMappingRsp.getPpNo();
        Integer testLineId = newCheckTestLineMappingRsp.getTestLineId();

        MappingBasicDataReq mappingBasicDataReq = new MappingBasicDataReq();
        mappingBasicDataReq.setCustomerGroupCode(customerGroupCode);
        mappingBasicDataReq.setProductLineCode(bu);
        mappingBasicDataReq.setModuleType(LowesAnalyte);
        mappingBasicDataReq.setFilters(Arrays.asList(value));
        mappingBasicDataReq.setQueryByCustomer("1");

        CustomResult<List<GetBasicDataMappingRsp>> basicDataMapping = localILayerClient.getBasicDataMapping(mappingBasicDataReq);
        if (!basicDataMapping.isSuccess()) {
            return null;
        }
        List<GetBasicDataMappingRsp> basicDataMappingData = basicDataMapping.getData();
        if (Func.isEmpty(basicDataMappingData)) {
            return null;
        }
        GetBasicDataMappingRsp getBasicDataMappingRsp = basicDataMappingData.get(0);
        GetBasicDataMappingRsp.BasicDataSgsRsp sgs = getBasicDataMappingRsp.getSgs();
        if (Func.isEmpty(sgs
        )) {
            return null;
        }
        String analyteId = sgs.getAnalyteId();
        return testLineId + splitFlag + ppNo + splitFlag + analyteId;
    }

    @Override
    public String getName() {
        return "getResultValueByTypeFn";
    }

    @Override
    public String desc() {
        return "getResultValueByTypeFn";
    }
}
