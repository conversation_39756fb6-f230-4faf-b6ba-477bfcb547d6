package com.sgs.customerbiz.biz.convert.impl.fn;

import cn.hutool.core.util.ArrayUtil;
import com.sgs.customerbiz.biz.convert.impl.fn.base.StringOperationFn;
import com.sgs.customerbiz.domain.domainservice.TrfReportDomainService;
import com.sgs.customerbiz.integration.ReportDataClient;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.tool.utils.Func;
import com.sgs.preorder.facade.model.enums.ReportStatus;
import com.sgs.testdatabiz.facade.model.dto.rd.report.ReportDataDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;


@Component
public class UNIQLOUpdateTypeFn extends StringOperationFn {

    @Autowired
    private ReportDataClient reportDataClient;

    @Autowired
    private TrfReportDomainService trfReportDomainService;



    @Override
    public Object invoke(Object[] args) {
        if (ArrayUtil.isEmpty(args) || args.length != 4) {
            return "U";
        }
        // reportNo
        Object reportNo = args[0];
        if (Func.isEmpty(reportNo)) {
            return "U";
        }
        // labCode
        Object labCode = args[1];
        // systemId
        Object systemId = args[2];
        // originalReportNo
        Object originalReportNo = args[3];
        if (Func.isNotEmpty(originalReportNo)) {
            reportNo = originalReportNo;
        }
        if (Func.isEmpty(systemId)) {
            systemId = trfReportDomainService.getSystemIdByReportNo(reportNo.toString());
        }
        // 此处拿到数据后调用RD获取report信息
        BaseResponse<List<ReportDataDTO>> reportInfo = this.getReportInfo(reportNo, labCode, systemId);
        // 获取不到信息返回null
        if (Func.isEmpty(reportInfo)) {
            return "U";
        }
        ReportDataDTO data = reportInfo.getData().get(0);
        // 获取当前报告的状态
        Integer reportStatus = data.getHeader().getReportStatus();
        // 如果当前报告有originalReportNo
        if (Func.isNotEmpty(originalReportNo)) {
            return "U";
        } else {
            // 当前没有originalReportNo
            if (Objects.equals(ReportStatus.Approved.getStatus(), reportStatus)) {
                return "U";
            } else if (Objects.equals(ReportStatus.Cancelled.getStatus(), reportStatus)) {
                return "D";
            }
        }
        return "U";
    }


    private BaseResponse<List<ReportDataDTO>> getReportInfo(Object reportNo,Object labCode,Object systemId) {
        return reportDataClient.getAllReportInfo(Arrays.asList(reportNo.toString()),labCode.toString(),Func.isEmpty(systemId)?null:Long.parseLong(systemId.toString()));
    }


    @Override
    public String getName() {
        return "uNIQLOUpdateTypeFn";
    }

    @Override
    public String desc() {
        return "uNIQLOUpdateTypeFn";
    }
}
