package com.sgs.customerbiz.biz.convert.impl.fn;

import com.alibaba.fastjson.JSONObject;
import com.sgs.customerbiz.biz.convert.impl.fn.base.StringOperationFn;
import com.sgs.customerbiz.integration.DffClient;
import com.sgs.customerbiz.integration.FrameWorkClient;
import com.sgs.customerbiz.integration.dto.DffMappingConfigRsp;
import com.sgs.framework.tool.utils.Func;
import com.sgs.preorder.facade.model.info.LabInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Optional;


@Slf4j
@Component
public class LabInfoFn extends StringOperationFn {

    @Autowired
    private FrameWorkClient frameWorkClient;

    @Override
    public Object invoke(Object[] args) {
        if (args == null || args.length != 2) {
            return null;
        }
        LabInfo info = frameWorkClient.getLabCodeInfoByLabCodeFromCache(args[0].toString(),null);
        if (Func.isEmpty(info)) {
            return null;
        }
        String locationName = info.getLocationName();

        String productLineName = info.getProductLineName();
        String string = args[1].toString();
        switch (string) {
            case "locationName":
                return locationName;
            case "productLineName":
                return productLineName;
        }

        return null;
    }


    @Override
    public String getName() {
        return "labInfoFn";
    }

    @Override
    public String desc() {
        return "labInfoFn";
    }
}
