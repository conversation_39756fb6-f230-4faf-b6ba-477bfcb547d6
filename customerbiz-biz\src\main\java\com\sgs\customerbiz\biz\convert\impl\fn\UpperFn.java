package com.sgs.customerbiz.biz.convert.impl.fn;

import com.sgs.customerbiz.biz.convert.impl.fn.base.StringOperationFn;
import org.springframework.stereotype.Component;

@Component
public class UpperFn extends StringOperationFn {

    @Override
    public Object invoke(Object[] args) {
        if (args == null || args.length != 1 || args[0] == null) {
            return null;
        }
        return args[0].toString().toUpperCase();
    }

    @Override
    public String getName() {
        return "upper";
    }

    @Override
    public String desc() {
        return "upper";
    }
}
