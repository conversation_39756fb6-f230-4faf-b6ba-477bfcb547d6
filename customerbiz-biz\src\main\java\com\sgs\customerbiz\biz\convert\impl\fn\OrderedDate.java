package com.sgs.customerbiz.biz.convert.impl.fn;

import com.sgs.customerbiz.biz.convert.impl.fn.base.DataConvertFn;
import com.sgs.customerbiz.model.tuple.Pair;
import com.sgs.customerbiz.validation.validator.TypeUtils;
import com.sgs.framework.tool.utils.Func;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.Collection;
import java.util.Comparator;
import java.util.Objects;
import java.util.stream.Collectors;

@Component
public class OrderedDate implements DataConvertFn {
    @Override
    public Object invoke(Object[] args) {
        if (Func.isEmpty(args)) {
            return null;
        }
        if (Func.isEmpty(args[0])) {
            return null;
        }

        Object dateList = args[0];
        String ordered = (args.length == 1 || Objects.isNull(args[1])) ? "asc" : args[1].toString();
        ordered = ordered.equals("desc") ? "desc" : "asc";
        if(!(dateList instanceof Collection)){
            throw new IllegalArgumentException("args[0] is not a collection");
        }
        Comparator<Pair<Object, LocalDateTime>> sorter = Comparator.comparing(Pair<Object, LocalDateTime>::getSecond);
        if(ordered.equals("desc")) {
            sorter = sorter.reversed();
        }
        return ((Collection<?>) dateList).stream()
                .filter(TypeUtils::canConvertToLocalDateTime)
                .map(date -> Pair.<Object,LocalDateTime>of(date, TypeUtils.convertToLocalDateTime(date)))
                .sorted(sorter)
                .map(Pair::getFirst)
                .collect(Collectors.toList());
    }

    @Override
    public String getName() {
        return "orderedDate";
    }

    @Override
    public String desc() {
        return "orderedDate";
    }
}

