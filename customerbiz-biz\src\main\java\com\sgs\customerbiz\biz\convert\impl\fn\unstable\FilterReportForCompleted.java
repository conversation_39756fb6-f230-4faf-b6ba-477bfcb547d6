package com.sgs.customerbiz.biz.convert.impl.fn.unstable;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONPath;
import com.sgs.customerbiz.biz.convert.impl.DataConvertContextHolder;
import com.sgs.customerbiz.biz.convert.impl.fn.base.UnstableConvertFn;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 已经废弃！！！！
 * 已经废弃！！！！
 * 已经废弃！！！！
 * 已经废弃！！！！
 * 已经废弃！！！！
 * 已经废弃！！！！
 * completed场景下过滤reportList
 * 参数；1.全量    2.增量
 * @author: shawn.yang
 * @create: 2023-07-21 17:24
 */
@Deprecated
@Component
public class FilterReportForCompleted implements UnstableConvertFn {

    private final static Integer FULL_MODE = 1;
    private final static Integer INCR_MODE = 2;


    @Override
    public Object invoke(Object[] args) {
        // 获取sourceData
        JSONObject sourceData = DataConvertContextHolder.getSourceData();
        if (sourceData ==null){
            return null;
        }

        // 未传入指定模式根据当前是reviseCompleted还是Completed来获取report
        if(args ==null || args.length==0||(args.length ==1 && StringUtils.isBlank(args[0].toString()))){
            // 1.获取originalReportNo
            List<String> originalReportNoList = this.getOriginalReportNoList(sourceData);
            boolean isReviseCompleted = (!originalReportNoList.isEmpty());
            // 2.如果是reviseCompleted，增量模式获取本次新增的报告
            return isReviseCompleted?this.filterByMode(sourceData,INCR_MODE):this.filterByMode(sourceData,FULL_MODE);
        }

        // 传入了指定模式
        if (args.length ==1){
            int filterMode = Integer.parseInt(args[0].toString());
            return this.filterByMode(sourceData,filterMode);
        }

        return null;
    }

    private List<String> getOriginalReportNoList(JSONObject sourceData){
        Object originalReportNoList = JSONPath.eval(sourceData, "$.extra.syncInfo.reportList.originalReportNo");

        if (!(originalReportNoList instanceof List)){
            return Collections.emptyList();
        }
        return ((List<?>) originalReportNoList).stream()
                .filter(originalReportNo -> Objects.nonNull(originalReportNo)
                        && (StringUtils.isNotEmpty(originalReportNo.toString()))).map(Object::toString).collect(Collectors.toList());
    }


    private Object filterByMode(JSONObject sourceData,int filterMode){
        // 全量模式
        if (FULL_MODE.equals(filterMode)){
            return this.getAllReport(sourceData);
        }
        if (INCR_MODE.equals(filterMode)){
            return this.getReviseReport(sourceData);
        }

        return null;
    }


    private List<Object> getReviseReport(JSONObject sourceData){
        // 获取本次同步reportNo
        List<String> originalReportNoList = this.getOriginalReportNoList(sourceData);
        if (originalReportNoList.isEmpty()){
            return Collections.emptyList();
        }

        Object reportList = JSONPath.eval(sourceData, "$.reportList");
        if (!(reportList instanceof List)){
            return Collections.emptyList();
        }

        return ((List<?>) reportList).stream().filter(Objects::nonNull)
                .map(r -> (JSONObject) r)
                .filter(report -> !originalReportNoList.contains(report.getString("reportNo"))).collect(Collectors.toList());
    }

    private Object getAllReport(JSONObject sourceData){
        return JSONPath.eval(sourceData, "$.reportList");
    }


    @Override
    public String getName() {
        return "filterReportForCompleted";
    }


    @Override
    public String desc() {
        return "completed场景下根据传入参数过滤reportList" +
                "1：全量\n" +
                "2：增量";
    }
}
