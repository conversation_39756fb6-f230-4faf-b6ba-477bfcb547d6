package com.sgs.config.api.service;

import com.sgs.config.api.dto.SystemApiDTO;

/**
 * @Desc
 * <AUTHOR>
 * @date 2023/12/7 11:41
 */
public interface SystemAPIConfigService {

    /**
     *获得API信息
     * @param systemId
     * @param apiId
     * @return
     */
    public SystemApiDTO getRequestApiInfo(Integer systemId, Long apiId) ;

    /**
     * 获得API信息
     * @param apiId
     * @return
     */
    public SystemApiDTO getRequestApiInfo(Long apiId) ;

    /**
     *
     */
    public SystemApiDTO getImportApiInfo(Integer refSystemId);

    SystemApiDTO getOrderToTrfApiInfo(Integer refSystemId);

}
