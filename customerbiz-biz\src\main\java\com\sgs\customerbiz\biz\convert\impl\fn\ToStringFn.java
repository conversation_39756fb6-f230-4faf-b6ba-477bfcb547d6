package com.sgs.customerbiz.biz.convert.impl.fn;

import cn.hutool.core.date.DateUtil;
import com.sgs.customerbiz.biz.convert.impl.fn.base.StringOperationFn;
import com.sgs.framework.tool.utils.Func;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Date;

@Slf4j
@Component
public class ToStringFn extends StringOperationFn {

    @Override
    public Object invoke(Object date) {
        if (Func.isEmpty(date)) {
            return null;
        }
        return date.toString();
    }

    @Override
    public String getName() {
        return "toStringFn";
    }

    @Override
    public String desc() {
        return "toString";
    }
}
