package com.sgs.config.api.dto.req;

import lombok.Data;

@Data
public class DataSend {
    /**
     * /**
     * 固定时间逐条发送，如每日凌晨4点单条发送
     * SINGLE_FIXED_TIME(1, "single_fixed_time"),
     * SINGLE_FIXED_RATE(2, "single_fixed_rate"),
     * BATCH_FIXED_TIME(3, "batch_fixed_time"),
     * BATCH_FIXED_RATE(4, "batch_fixed_rate");
     */

    private Integer mode;

    /**
     * 时间表达式
     * cronExpression
     */
    private String cronExpression;

    /**
     * 对应系统id
     */
    private Integer identityId;
}
