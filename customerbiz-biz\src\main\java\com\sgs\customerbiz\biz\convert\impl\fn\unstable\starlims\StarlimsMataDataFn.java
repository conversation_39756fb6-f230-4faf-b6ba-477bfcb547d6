package com.sgs.customerbiz.biz.convert.impl.fn.unstable.starlims;

import com.alibaba.fastjson.JSONArray;
import com.sgs.customerbiz.biz.convert.impl.fn.base.StringOperationFn;
import com.sgs.customerbiz.model.trf.dto.TrfProductAttrLangDTO;
import com.sgs.customerbiz.model.trf.dto.TrfSampleAttrDTO;
import com.sgs.framework.tool.utils.Func;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Stream;

@Slf4j
@Component
public class StarlimsMataDataFn extends StringOperationFn {

    @Override
    public Object invoke(Object[] arg) {
        if (Func.isEmpty(arg)) {
            return null;
        }

        Stream<TrfSampleAttrDTO> finalStream = toStream(arg);
        if(Objects.isNull(finalStream)) {
            return null;
        }
        List<StarLimsMetaDataReq> list = new ArrayList<>();
        finalStream.forEach(
                trfSampleAttrDTO -> {
                    List<TrfProductAttrLangDTO> languageList = trfSampleAttrDTO.getLanguageList();
                    if (Func.isNotEmpty(languageList)) {
                        languageList.forEach(
                                lang -> {
                                    StarLimsMetaDataReq starLimsMetaDataReq = new StarLimsMetaDataReq();
                                    starLimsMetaDataReq.setMasterCode(trfSampleAttrDTO.getLabelCode());
                                    starLimsMetaDataReq.setValue(lang.getLabelValue());
                                    starLimsMetaDataReq.setLangid(Objects.equals(lang.getLanguageId(), 1) ? "ENG" : "ZHS");
                                    list.add(starLimsMetaDataReq);
                                }
                        );
                    }

                }
        );
        return list;
    }

    @Nullable
    private static Stream<TrfSampleAttrDTO> toStream(Object[] arg) {
        if(arg.length==1) {
            return toStream(arg[0]).orElse(null);
        }
        Optional<Stream<TrfSampleAttrDTO>> mataDataStream1 = toStream(arg[0]);
        Optional<Stream<TrfSampleAttrDTO>> trfSampleAttrDTOStream = toStream(arg[1]);
        if(!mataDataStream1.isPresent() && !trfSampleAttrDTOStream.isPresent()) {
            return null;
        }
        if (mataDataStream1.isPresent() && trfSampleAttrDTOStream.isPresent()) {
            return Stream.concat(mataDataStream1.get(), trfSampleAttrDTOStream.get());
        }
        return mataDataStream1.orElseGet(trfSampleAttrDTOStream::get);
    }

    @NotNull
    private static Optional<Stream<TrfSampleAttrDTO>> toStream(Object mataData2) {
        return Optional.ofNullable(mataData2).map(Object::toString).map(str -> JSONArray.parseArray(str, TrfSampleAttrDTO.class)).map(Collection::stream);
    }

    @Data
    public static final class StarLimsMetaDataReq {
        private String masterCode;
        private String value;
        private String langid;
    }

    @Override
    public String getName() {
        return "starlimsMataDataFn";
    }

    @Override
    public String desc() {
        return "starlimsMataDataFn";
    }
}
