package com.sgs.customerbiz.biz.convert.impl.fn;

import com.sgs.customerbiz.biz.convert.impl.fn.base.StringOperationFn;
import com.sgs.framework.tool.utils.Func;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Optional;

@Slf4j
@Component
public class SubStringFn extends StringOperationFn {

    @Override
    protected Object invoke(Object arg1, Object arg2) {
        if(Func.isEmpty(arg2)) {
            throw new IllegalArgumentException("substring size is empty");
        }
        int size = 0;
        try {
            size = Integer.parseInt(arg2.toString());
        } catch (NumberFormatException e) {
            throw new IllegalArgumentException("substring size is not integer");
        }
        String strVal = Optional.ofNullable(arg1).map(String::valueOf).orElse("");
        int len = strVal.length();
        if(len <= size) {
            return strVal;
        }
        // 如果A的长度超过B，从A中截取B长度的子串
        return strVal.substring(0, size);
    }

    @Override
    public String getName() {
        return "substring";
    }

    @Override
    public String desc() {
        return "substring";
    }
}
