package com.sgs.customerbiz.biz.convert.impl.fn.unstable;

import org.springframework.stereotype.Component;

import com.sgs.customerbiz.biz.convert.impl.fn.base.StringOperationFn;

@Component
public class TimestampFn extends StringOperationFn {
    

    @Override
    public Object invoke(Object arg1) {
        long timestamp = System.currentTimeMillis();
        if (arg1 != null && "unix".equalsIgnoreCase(arg1.toString())) {
            return timestamp / 1000;
        }
        return timestamp;
    }

    @Override
    public String getName() {
        return "timestamp";
    }

    @Override
    public String desc() {
        return "timestamp";
    }
}
