package com.sgs.customerbiz.biz.convert.impl.fn;

import com.sgs.customerbiz.biz.convert.impl.fn.base.AbstractDataConvertFn;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Component
public class SetToStringFn extends AbstractDataConvertFn {

    @Override
    protected Object invoke(Object arg) {
        if (arg == null) {
            return "";
        }
        if (!(arg instanceof List)) {
            return arg.toString();
        }

        List<?> list = (List<?>) arg;
        return list.stream()
                .filter(Objects::nonNull)
                .map(Object::toString)
                .filter(StringUtils::isNotBlank)
                .distinct()
                .collect(Collectors.joining(","));
    }

    @Override
    public String getName() {
        return "setToStr";
    }

    @Override
    public String desc() {
        return "把一个list转成字符串并去重,使用逗号分割";
    }
}