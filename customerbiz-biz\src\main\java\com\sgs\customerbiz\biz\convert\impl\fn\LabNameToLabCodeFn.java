package com.sgs.customerbiz.biz.convert.impl.fn;

import com.sgs.customerbiz.biz.convert.impl.fn.base.StringOperationFn;
import com.sgs.customerbiz.integration.FrameWorkClient;
import com.sgs.framework.core.exception.Assert;
import com.sgs.preorder.facade.model.info.LabInfo;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * @author: shawn.yang
 * @create: 2023-06-12 15:36
 */
@Deprecated
//@Component
public class LabNameToLabCodeFn extends StringOperationFn {

    @Autowired
    private FrameWorkClient frameWorkClient;

    @Override
    public Object invoke(Object[] args) {
        Object arg1 = args[0];
        Assert.notNull(arg1,"labName cannot null");
        LabInfo labInfo = frameWorkClient.getLabCodeInfoByLabName((String) arg1);
        return labInfo==null?null:labInfo.getLaboratoryID();
    }

    @Override
    public String getName() {
        return "labNameToLabCode";
    }

    @Override
    public String desc() {
        return null;
    }
}
