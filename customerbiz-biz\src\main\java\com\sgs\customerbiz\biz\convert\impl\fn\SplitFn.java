package com.sgs.customerbiz.biz.convert.impl.fn;

import com.sgs.customerbiz.biz.convert.impl.fn.base.StringOperationFn;
import com.sgs.framework.tool.utils.Func;
import org.springframework.stereotype.Component;

import java.util.Arrays;

/**
 * <AUTHOR> walley.wang
 * @date         : 2023-07-25 16:00
 * @version      : V1.0.0
 * @desc         :
 */
@Component("splitFn")
public class SplitFn extends StringOperationFn {
    @Override
    public Object invoke(Object[] args) {
        if (args == null || args.length == 0 || args[0] == null) {
            return null;
        }

        // 默认分隔符为,
        String delimiter = ",";
        String source = args[0].toString();
        if (args.length == 2) {
            // 自定义分隔符
            delimiter = args[1].toString();
        }
        if (args.length == 3) {
            if (Func.isNotEmpty(args[1])) {
                delimiter = args[1].toString();
            }
            int i = Integer.parseInt(args[2].toString());
            String[] split = source.split(delimiter);
            return split[i];
        }

        return Arrays.asList(source.split(delimiter));
    }

    @Override
    public String getName() {
        return "splitStr";
    }

    @Override
    public String desc() {
        return "将不为空的字符串按指定分隔符切割成字符集合";
    }
}
