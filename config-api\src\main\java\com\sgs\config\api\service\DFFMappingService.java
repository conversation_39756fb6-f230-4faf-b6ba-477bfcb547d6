package com.sgs.config.api.service;

import com.sgs.config.api.dto.DFFMappingDTO;
import com.sgs.config.api.dto.req.ConfigGetReq;
import com.sgs.config.api.dto.req.DFFMappingQuery;

import java.util.List;

/**
 * @Desc
 * <AUTHOR>
 * @date 2024/1/9 14:28
 */
public interface DFFMappingService {

    /**
     *
     * @param configGetReq
     * @return
     */
    public List<DFFMappingDTO> getDFFMapping(ConfigGetReq configGetReq);

    List<DFFMappingDTO> getDFFMapping(DFFMappingQuery query);

}
