package com.sgs.customerbiz.biz.convert.impl.fn;

import com.sgs.customerbiz.biz.convert.impl.fn.base.StringOperationFn;
import com.sgs.customerbiz.domain.domainevent.*;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * @author: lianyq
 * @create: 2023-11-15 22:22
 */
@Component
public class GetTypeDescFn extends StringOperationFn {
    private static final Map<String, String> CANCEL_MAP = new HashMap<>();
    private static final Map<String, String> PENDING_MAP = new HashMap<>();

    static {
        CANCEL_MAP.put("1", "client require");
        CANCEL_MAP.put("2", "dummary validation");
        CANCEL_MAP.put("3", "operation fix");
        PENDING_MAP.put("1", "Waiting for payment");
        PENDING_MAP.put("2", "Waiting for client information/confirmation");
        PENDING_MAP.put("3", "Client requested to hold");
        PENDING_MAP.put("4", "Waiting for additional sample");
        PENDING_MAP.put("5", "Other special reasons,pls contact with CS");
    }

    @Override
    public Object invoke(Object[] args) {
        if (args == null) {
            return null;
        }
        Object arg = args[0];
        if (arg == null) {
            return null;
        }
        String actionType = arg.toString();

        if (actionType.equals(TrfPreOrderCancelEvent.class.getName()) || actionType.equals(TrfCancelOrderEvent.class.getName())) {
            Object cancelType = args[1];
            if (null == cancelType) {
                return null;
            }
            return CANCEL_MAP.get(cancelType.toString());
        }

        if (actionType.equals(TrfPendingEvent.class.getName()) || actionType.equals(TrfUnPendingEvent.class.getName())) {
            Object pendingType = args[2];
            if (null == pendingType) {
                return null;
            }
            String type = (String) pendingType;
            return PENDING_MAP.get(type);
        }
        return null;
    }


    @Override
    public String getName() {
        return "getTypeDescFn";
    }

    @Override
    public String desc() {
        return null;
    }
}
