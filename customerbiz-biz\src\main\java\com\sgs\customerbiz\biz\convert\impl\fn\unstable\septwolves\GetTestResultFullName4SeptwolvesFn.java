package com.sgs.customerbiz.biz.convert.impl.fn.unstable.septwolves;

import com.alibaba.fastjson.JSONObject;
import com.sgs.customerbiz.biz.convert.impl.fn.ConcatFn;
import com.sgs.customerbiz.biz.convert.impl.fn.base.DataConvertFn;
import com.sgs.customerbiz.model.trf.dto.TrfProductAttrDTO;
import com.sgs.customerbiz.model.trf.dto.TrfProductAttrLangDTO;
import com.sgs.framework.tool.utils.Func;
import com.sgs.testdatabiz.facade.model.dto.rd.report.RdTestResultDTO;
import com.sgs.testdatabiz.facade.model.dto.rd.report.RdTestResultLanguageDTO;
import com.sgs.testdatabiz.facade.model.dto.rd.report.RdTestResultResultDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @Desc
 * <AUTHOR>
 * @date 2024/2/27 16:47
 */
@Slf4j
@Component
public class GetTestResultFullName4SeptwolvesFn implements DataConvertFn {
    @Override
    public Object invoke(Object[] args) {
        if (Func.isEmpty(args)) {
            return null;
        }
        if (Func.isEmpty(args[0])) {
            return null;
        }

        Object rdTestResultStr = args[0];
        Object languageId = args[1];
        RdTestResultDTO rdTestResultDTO = JSONObject.parseObject(rdTestResultStr.toString(), RdTestResultDTO.class);
        if (Func.isEmpty(languageId)) {
            return Func.isNotEmpty(rdTestResultDTO.getTestResult()) ? rdTestResultDTO.getTestResult().getTestResultFullName() : null;
        } else {
            List<RdTestResultLanguageDTO> rdTestResultLanguageDTOList = rdTestResultDTO.getLanguageList();
            if (Func.isEmpty(rdTestResultLanguageDTOList)) {
                return rdTestResultDTO.getTestResult().getTestResultFullName();
            }
            RdTestResultLanguageDTO rdTestResultLanguageDTO = rdTestResultLanguageDTOList.stream().filter(i -> Func.equals(String.valueOf(languageId), String.valueOf(i.getLanguageId()))).findFirst().orElse(null);
            RdTestResultResultDTO testResult = rdTestResultDTO.getTestResult();
            if (Func.isEmpty(testResult)) {
                testResult = new RdTestResultResultDTO();
            }
            String value = testResult.getTestResultFullName();
            if (Func.isNotEmpty(rdTestResultLanguageDTO) && Func.isNotBlank(rdTestResultLanguageDTO.getTestResultFullName())) {
                value = rdTestResultLanguageDTO.getTestResultFullName();
            }
            return value;
        }

    }

    @Override
    public String getName() {
        return "getTestResultFullName4SeptwolvesFn";
    }

    @Override
    public String desc() {
        return "getTestResultFullName4SeptwolvesFn";
    }
}
