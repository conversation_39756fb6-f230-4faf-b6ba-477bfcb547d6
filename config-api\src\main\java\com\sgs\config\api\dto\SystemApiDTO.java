package com.sgs.config.api.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Objects;

/**
 * @Desc
 * <AUTHOR>
 * @date 2023/12/7 11:35
 */
@Data
public class SystemApiDTO implements Serializable {

    public static final String SPLIT_BY_TRF = "Trf";
    public static final String SPLIT_BY_REPORT = "Report";

    /**
     * 系统API ID
     */
    private Long id;

    /**
     * 系统标识符
     */
    private Integer systemId;

    /**
     * 协议类型 1.http 2.queue
     */
    private Integer protocolType;

    /**
     * 请求地址
     */
    private String requestUrl;

    /**
     * 请求方法 1.get 2.post
     */
    private Integer requestMethod;

    /**
     * 返回数据的模板
     */
    private String responseBodyTemplate;

    /**
     * 请求数据的模板
     */
    private String requestBodyTemplate;

    // 加密使用的类,一个spring beanName
    private String encipher;

    //拆分数据配置 default is Trf, Report
    private String splitConfig;

    public boolean splitByReport() {
        if(Objects.isNull(getSplitConfig())) {
            return false;
        }
        return getSplitConfig().equals(SPLIT_BY_REPORT);
    }
}
