package com.sgs.config.impl.manager;

import com.sgs.customerbiz.dbstorages.mybatis.mapper.todolist.SystemApiMapper;
import com.sgs.customerbiz.dbstorages.mybatis.model.SystemApiPO;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * @author: shawn.yang
 * @create: 2023-05-11 20:19
 */
@Component
public class SystemApiManager {
    private final SystemApiMapper systemApiMapper;


    public SystemApiPO findByApiId(Long apiId){
        if (apiId ==null){
            return null;
        }
        return systemApiMapper.selectByPrimaryKey(apiId);
    }


    public SystemApiPO findBySystemIdAndApiId(Integer systemId,Long apiId){
        if (systemId ==null|| apiId ==null){
            return null;
        }

        SystemApiPO systemApi = findByApiId(apiId);
        if (systemApi ==null){
            return null;
        }
        return Objects.equals(systemApi.getSystemId(),systemId)?systemApi:null;
    }


    public SystemApiManager(SystemApiMapper systemApiMapper) {
        this.systemApiMapper = systemApiMapper;
    }
}
