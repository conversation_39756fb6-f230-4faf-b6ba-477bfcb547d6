package com.sgs.config.api.service;

import com.sgs.config.api.dto.ConfigCustomerData;
import com.sgs.config.api.dto.ConfigInfo;
import com.sgs.config.api.dto.req.ConfigGetReq;

import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 */
public interface ConfigService {

    Optional<ConfigCustomerData> getCustomerData(Integer refSystemId);

    ConfigInfo getConfig(ConfigGetReq configGetReq);

    List<String> getSupportEmailList(Integer systemId);

    List<ConfigInfo> getConfigList(ConfigGetReq configGetReq);

    List<String> getCustomerIds(Integer refSystemId);


    List<String> getCustomerGroupCodeByRefSystemId(Integer refSystemId);
}
