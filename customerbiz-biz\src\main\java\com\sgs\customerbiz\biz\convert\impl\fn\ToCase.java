package com.sgs.customerbiz.biz.convert.impl.fn;

import com.sgs.customerbiz.biz.convert.impl.fn.base.StringOperationFn;
import org.springframework.stereotype.Component;

import java.util.Objects;

@Component
public class ToCase extends StringOperationFn {

    @Override
    protected Object invoke(Object arg1) {
        return invoke(arg1, null);
    }

    @Override
    public Object invoke(Object data, Object arg2) {
        if (Objects.isNull(data)) {
            return null;
        }
        boolean upper = Objects.isNull(arg2) || arg2.toString().equals("upper");
        return  upper ? data.toString().toUpperCase() : data.toString().toLowerCase();
    }

    @Override
    public String getName() {
        return "toCase";
    }

    @Override
    public String desc() {
        return "toCase";
    }
}
