package com.sgs.customerbiz.biz.convert.impl.fn.unstable.walmart;

import com.alibaba.nacos.common.utils.Objects;
import com.sgs.customerbiz.biz.convert.impl.fn.base.StringOperationFn;
import org.springframework.stereotype.Component;

import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Component
public class LabNumberFn extends StringOperationFn {

    @Override
    protected Object invoke(Object arg1, Object arg2, Object arg3, Object arg4) {
        if(Objects.isNull(arg1)) {
            return "";
        }

        if(Objects.isNull(arg2)) {
            throw new IllegalArgumentException("必须提供 bossNo (arg2)");
        }
        String bossNo = arg1.toString();
        Set<String> rootReportNoByBossNo = Stream.of(arg2.toString().split("-")).collect(Collectors.toSet());
        return rootReportNoByBossNo.contains(bossNo) ? arg3 : arg4;
    }

    @Override
    public String getName() {
        return "labNumber";
    }

    @Override
    public String desc() {
        return "labNumber";
    }
}
