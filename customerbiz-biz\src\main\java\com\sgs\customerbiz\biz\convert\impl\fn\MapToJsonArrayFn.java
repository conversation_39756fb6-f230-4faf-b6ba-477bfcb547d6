package com.sgs.customerbiz.biz.convert.impl.fn;

import com.alibaba.fastjson.JSONArray;
import com.sgs.customerbiz.biz.convert.impl.fn.base.AbstractDataConvertFn;

import java.util.Map;

/**
 * @author: shawn.yang
 * @create: 2023-07-24 13:43
 */
//@Component
public class MapToJsonArrayFn extends AbstractDataConvertFn {
    @Override
    public Object invoke(Object argMap) {
        if (!(argMap instanceof Map)){
            throw new IllegalArgumentException(String.format("illegal args[0] type:expected 'Map' ,actual: %s",(argMap ==null)?"null":argMap.getClass().getSimpleName()));
        }

        return toJsonArray((Map<String, Object>) argMap);
    }

    private JSONArray toJsonArray(Map<String,Object> map){
        if (map.isEmpty()){
            return new JSONArray();
        }
        JSONArray array = new JSONArray();
        array.addAll(map.entrySet());

        return array;
    }

    @Override
    public String getName() {
        return "mapToJsonArray";
    }

    @Override
    public String desc() {
        return "可以把一个map类型(JsonObject也是map类型)转成json array。\n" +
                "例；\n" +
                "{name:yaofeng,id:9527} ====> [{key:name,value:yaofeng},{key:id,value:9527}]";
    }
}
