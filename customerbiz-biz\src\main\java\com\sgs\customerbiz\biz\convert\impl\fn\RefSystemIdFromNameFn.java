package com.sgs.customerbiz.biz.convert.impl.fn;

import com.sgs.customerbiz.biz.convert.impl.fn.base.StringOperationFn;
import com.sgs.framework.model.enums.RefSystemIdEnum;
import org.springframework.stereotype.Component;

/**
 * 通过name获取refSystemId
 * @author: shawn.yang
 * @create: 2023-09-07 10:06
 */
@Component
public class RefSystemIdFromNameFn extends StringOperationFn {
    @Override
    protected Object invoke(Object arg1) {
        if (arg1 ==null ||arg1.toString().isEmpty()){
            return null;
        }
        for (RefSystemIdEnum value : RefSystemIdEnum.values()) {
            String refSystemName = subtractQuote(arg1.toString());
            if (value.name().equals(refSystemName)) {
                return value.getRefSystemId();
            }
        }
        return null;
    }

    @Override
    public String getName() {
        return "refSystemId";
    }

    @Override
    public String desc() {
        return "通过RefSystemName获取RefSystemId";
    }
}
