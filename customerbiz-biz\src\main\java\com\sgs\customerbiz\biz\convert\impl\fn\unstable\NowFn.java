package com.sgs.customerbiz.biz.convert.impl.fn.unstable;

import cn.hutool.core.date.DateUtil;
import com.sgs.customerbiz.biz.convert.impl.fn.base.StringOperationFn;
import com.sgs.framework.tool.utils.Func;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Date;
@Slf4j
@Component
public class NowFn extends StringOperationFn {
    private static String DEFAULT_FORMAT = "yyyy-MM-dd HH:mm:ss";

    @Override
    public Object invoke(Object arg1) {
        if (Func.isNotEmpty(arg1)) {
            DEFAULT_FORMAT = arg1.toString();
        }
        return formatDate(new Date(), DEFAULT_FORMAT);
    }

    private String formatDate(Object date, String format) {
        try {
            if (date == null) {
                return null;
            }
            format = subtractQuote(format);
            if (date instanceof Date) {
                return DateUtil.format((Date) date, format);
            } else if (date instanceof Long) {
                return DateUtil.format(new Date((long) date), format);
            } else if (date instanceof String) {
                return DateUtil.format(DateUtil.parse(date.toString()), format);
            }
        } catch (Exception e) {
            log.error("ToDateFn parse date error. date:{} ,format:{}", date, format);
            throw new IllegalArgumentException("format date error");
        }

        log.error("unsupported date type. type:{}", date.getClass().getSimpleName());
        throw new IllegalArgumentException("unsupported date type");
    }

    private Date formatDate(Object date) {
        try {
            if (date == null) {
                return null;
            }
            if (date instanceof Date) {
                return (Date) date;
            } else if (date instanceof Long) {
                return new Date((long) date);
            } else if (date instanceof String) {
                return DateUtil.parse(date.toString());
            }
        } catch (Exception e) {
            log.error("ToDateFn parse date error. date:{} ,format:{}", date);
            throw new IllegalArgumentException("format date error");
        }
        log.error("unsupported date type. type:{}", date.getClass().getSimpleName());
        throw new IllegalArgumentException("unsupported date type");
    }


    @Override
    public String getName() {
        return "now";
    }

    @Override
    public String desc() {
        return "接收两个参数：" +
                "param1:时间,接收【时间戳、时间字符串、Date】三种类型  " +
                "param2:格式（可选，默认用‘yyyy-MM-dd HH:mm:ss’）";
    }
}
