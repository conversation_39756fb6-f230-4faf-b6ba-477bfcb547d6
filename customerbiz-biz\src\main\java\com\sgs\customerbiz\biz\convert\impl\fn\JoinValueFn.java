package com.sgs.customerbiz.biz.convert.impl.fn;

import com.sgs.customerbiz.biz.convert.impl.fn.base.StringOperationFn;
import com.sgs.framework.tool.utils.Func;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.stream.Collectors;

@Component
public class JoinValueFn extends StringOperationFn {
    @Override
    public Object invoke(Object[] args) {
        if (args ==null || args.length ==0) {
            return null;
        }

        StringBuilder sb = new StringBuilder();

        for (Object arg : args) {
            if (Func.isEmpty(arg)) {
                continue;
            }
            sb.append(arg.toString());
        }
        return sb.toString();
    }

    @Override
    public String getName() {
        return "joinValueFn";
    }

    @Override
    public String desc() {
        return "joinValueFn";
    }
}
