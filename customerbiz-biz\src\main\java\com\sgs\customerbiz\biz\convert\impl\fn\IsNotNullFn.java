package com.sgs.customerbiz.biz.convert.impl.fn;

import cn.hutool.core.util.ArrayUtil;
import com.sgs.customerbiz.biz.convert.impl.fn.base.StringOperationFn;
import com.sgs.framework.tool.utils.Func;
import org.springframework.stereotype.Component;

/**
 * 通用mapping，支持输入一个参数
 * param1： key
 * param3： 默认值
 * @author: shawn.yang
 * @create: 2023-06-30 09:30
 */
@Component
public class IsNotNullFn extends StringOperationFn {
    @Override
    public Object invoke(Object[] args) {
        if (ArrayUtil.isEmpty(args)){
            return null;
        }
        Object key = args[0];

        if (key ==null ){
            throw new NullPointerException("MappingFn args");
        }

        String s = subtractQuote(key.toString());
        if (Func.isNotEmpty(s)) {
            return "true";
        }
        return "false";
    }


    @Override
    public String getName() {
        return "isNotNull";
    }

    @Override
    public String desc() {
        return "isNotNull";
    }
}
