package com.sgs.customerbiz.biz.convert.impl.fn.unstable.walmart;

import com.alibaba.fastjson.JSONObject;
import com.sgs.customerbiz.biz.convert.impl.fn.base.StringOperationFn;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.Collections;
import java.util.stream.Collectors;

@Component
public class PriorTestingFn extends StringOperationFn {

    @Override
    public String getName() {
        return "priorTesting";
    }

    @Override
    public String desc() {
        return "priorTesting";
    }

    @Override
    protected Object invoke(Object arg1) {
        if (arg1 == null || !(arg1 instanceof String)) {
            return Collections.emptyList();
        }

        String input = (String) arg1;
        return Arrays.stream(input.split(","))
                .map(String::trim)
                .filter(StringUtils::isNotBlank)
                .map(item -> {
                    JSONObject jsonObject = new JSONObject();
                    jsonObject.put("lab_number", item);
                    return jsonObject;
                })
                .collect(Collectors.toList());
    }
}
