package com.sgs.config.api.enums;

public enum ModeEnum {


    /**
     * 固定时间逐条发送，如每日凌晨4点单条发送
     */
    SINGLE_FIXED_TIME(1, "single_fixed_time"),
    /**
     * 每隔30s逐条发送
     */
    SINGLE_FIXED_RATE(2, "single_fixed_rate"),
    /**
     * 固定时间批量发送，如每日凌晨4点批量发送，不固定数量
     */
    BATCH_FIXED_TIME(3, "batch_fixed_time"),
    /**
     * 每隔30s批量发送，不固定数量
     */
    BATCH_FIXED_RATE(4, "batch_fixed_rate");


    private final Integer code;
    private final String desc;

    ModeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static ModeEnum from(Integer code) {
        if (code == null) {
            return null;
        }
        for (ModeEnum value : values()) {
            if (value.code.equals(code)) {
                return value;
            }
        }
        return null;
    }

    public static boolean checkIsFixedTime(Integer code) {
        if (SINGLE_FIXED_TIME.getCode() == code || BATCH_FIXED_TIME.getCode() == code) {
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }

    public static boolean checkIsBatchDeal(Integer code) {
        if (BATCH_FIXED_RATE.getCode() == code || BATCH_FIXED_TIME.getCode() == code) {
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }
}
