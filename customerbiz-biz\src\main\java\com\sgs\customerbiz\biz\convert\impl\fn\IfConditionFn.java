package com.sgs.customerbiz.biz.convert.impl.fn;

import cn.hutool.core.util.ArrayUtil;
import com.sgs.customerbiz.biz.constants.Constant;
import com.sgs.customerbiz.biz.convert.impl.fn.base.AbstractDataConvertFn;
import com.sgs.framework.tool.utils.Func;
import org.springframework.stereotype.Component;

@Component
public class IfConditionFn extends AbstractDataConvertFn {

    /**
     * 可以比较以下几种情况：
     * 1） args[0]< args[1]
     * 2) args[0]> args[1]
     * 3) args[0] = args[1]
     *
     * @param args
     * @return
     */
    @Override
    public Object invoke(Object[] args) {
        if (ArrayUtil.isEmpty(args) || args.length < 2) {
            return null;
        }
        Object value1 = args[0];
        if (Func.isEmpty(value1)) {
            return null;
        }

        Object value2 = args[1];
        if (Func.isEmpty(value2)) {
            return null;
        }

        Object value3 = args[2];
        if (Func.isEmpty(value3)) {
            return null;
        }

        String symbol = (String) value2;

        if (symbol.contains(Constant.GREATER_THAN)) {
            return greaterThan(value1, value3);
        }
        if (symbol.contains(Constant.LESS_THAN)) {
            return lessThan(value1, value3);
        }
        if (symbol.contains(Constant.EQUAL_TO)) {
            return equal(value1, value3);
        }
        if (symbol.contains(Constant.NotNull)) {
            return notNull(value1, value3);
        }
        return null;
    }


    @Override
    public String getName() {
        return "ifConditionFn";
    }

    @Override
    public String desc() {
        return "ifConditionFn";
    }

    private Object greaterThan(Object value1, Object value3) {
        return lessThan(value3, value1);
    }

    private Object lessThan(Object value1, Object value3) {
        if (value1 instanceof Integer && value3 instanceof Integer) {
            // 如果都是整数，进行整数比较
            if (((Integer) value1).intValue() < ((Integer) value3).intValue()) {
                return value3;
            } else {
                return null;
            }
        }
        return null;
    }

    private Object equal(Object value1, Object value3) {
        if (value1 instanceof String && value3 instanceof String) {
            // 如果都是字符串，进行字符串比较
            if (value1.equals(value3)) {
                return value3;
            } else {
                return null;
            }
        } else if (value1 instanceof Integer && value3 instanceof Integer) {
            // 如果都是整数，进行整数比较
            if (value1.equals(value3)) {
                return value3;
            } else {
                return null;
            }
        } else {
            // 数据类型不匹配，无法比较
            return null;
        }
    }

    private Object notNull(Object value1, Object value3) {
        String s = value3.toString();
        String[] split = s.split(":");
        return Func.isNotEmpty(value1) ? split[0] : split[1];
    }
}
