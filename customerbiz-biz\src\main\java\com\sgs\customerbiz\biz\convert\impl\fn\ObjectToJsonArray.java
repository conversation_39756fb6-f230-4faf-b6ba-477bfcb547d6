package com.sgs.customerbiz.biz.convert.impl.fn;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.sgs.customerbiz.biz.convert.impl.fn.base.StringOperationFn;
import org.springframework.stereotype.Component;

import java.util.Objects;

@Component
public class ObjectToJsonArray extends StringOperationFn {

    @Override
    protected Object invoke(Object arg1,Object arg2, Object arg3, Object arg4) {
        if(Objects.isNull(arg3) || Objects.isNull(arg4)) {
            throw new IllegalArgumentException("arg3 and arg4 must not be null");
        }
        JSONObject obj = new JSONObject();
        obj.put(arg3.toString(), arg1);
        obj.put(arg4.toString(), arg2);
        JSONArray list = new JSONArray();
        list.add(obj);
        return list;
    }

    @Override
    public String getName() {
        return "objectToJSONArray";
    }

    @Override
    public String desc() {
        return "objectToJSONArrayFn";
    }
}
