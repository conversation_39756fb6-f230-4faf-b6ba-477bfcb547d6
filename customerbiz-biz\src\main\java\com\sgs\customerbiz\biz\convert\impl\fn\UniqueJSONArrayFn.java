package com.sgs.customerbiz.biz.convert.impl.fn;

import com.alibaba.fastjson.JSONArray;
import com.sgs.customerbiz.biz.convert.impl.fn.base.StringOperationFn;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.LinkedHashSet;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Component
public class UniqueJSONArrayFn extends StringOperationFn {

    @Override
    protected Object invoke(Object arg1) {
        return invoke(arg1, null);
    }

    @Override
    protected Object invoke(Object arg1, Object arg2) {
        if (!(arg1 instanceof Collection)) {
            return arg1;
        }

        Collection<?> collection = (Collection<?>) arg1;
        String propertyName = arg2 instanceof String ? (String) arg2 : null;

        if (collection.isEmpty()) {
            return arg1;
        }

        // 如果propertyName为空，基于整个对象去重
        if (StringUtils.isBlank(propertyName)) {
            Set<Map<?, ?>> uniqueElements = new LinkedHashSet<>();
            return collection.stream()
                    .filter(item -> item instanceof Map)
                    .map(item -> (Map<?, ?>) item)
                    .filter(uniqueElements::add)
                    .collect(Collectors.toCollection(JSONArray::new));
        }

        // 基于指定属性去重
        Set<Object> uniquePropertyValues = new LinkedHashSet<>();
        return collection.stream()
                .filter(item -> item instanceof Map)
                .map(item -> (Map<?, ?>) item)
                .filter(item -> {
                    Object value = item.get(propertyName);
                    return value != null && uniquePropertyValues.add(value);
                })
                .collect(Collectors.toCollection(JSONArray::new));
    }

    @Override
    public String getName() {
        return "uniqueJSONArray";
    }

    @Override
    public String desc() {
        return "对JSONArray进行去重，支持基于整个对象或指定属性进行去重";
    }
}
