package com.sgs.config.api.constant;

public class ConfigKeyConstant {

//    public static final String SINGLE_FIXED_TIME = "single_fixed_time";
//    public static final String SINGLE_FIXED_RATE = "single_fixed_rate";
//
//    public static final String BATCH_FIXED_TIME = "batch_fixed_time";
//
//    public static final String BATCH_FIXED_RATE = "batch_fixed_rate";

    /**
     * 客户报告发送模式：RealTime | Wait All
     */
    public static final String CUSTOMER_REPORT_SYNC_MODE = "CustomerReportSyncMode";

    /**
     * Trf import 的导入模式：PUSH | PULL
     */
    public static final String CONFIG_TRF_IMPORT_MODE = "Trf.ImportMode";

    /**
     * Trf 导入的源类型： TrfToOrder | OrderToTrf
     */
    public static final String CONFIG_TRF_IMPROT_MODE_TRF_SOURCE_TYPE = "Trf.ImportMode.TrfSourceType";

    /**
     * CustomerTrf 导入：转换为SgsTrf 的模板
     */
    public static final String CONFIG_CUSTOMERTRF_IMPORT_CONVERT_TEMPLATE_TO_SGSTRF = "CustomerTrf.Import.Template.SgsTrf";

    /**
     * Order To Trf ：转换为SgsTrf 模板
     */
    public static final String CONFIG_ORDER_IMPORT_CONVERT_TEMPLATE_TO_SGSTRF = "Order.Import.Template.SgsTrf";
}
