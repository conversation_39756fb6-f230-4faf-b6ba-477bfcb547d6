package com.sgs.config.impl.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Assert;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONPath;
import com.google.common.collect.ArrayListMultimap;
import com.google.common.collect.Multimap;
import com.sgs.config.api.dto.ConfigCustomerData;
import com.sgs.config.api.dto.ConfigInfo;
import com.sgs.config.api.dto.req.ConfigGetReq;
import com.sgs.config.api.service.ConfigService;
import com.sgs.config.impl.domain.ConfigInfoDomainService;
import com.sgs.config.impl.domain.domainobject.ConfigInfoDO;
import com.sgs.customerbiz.core.constants.Constants;
import com.sgs.framework.core.exception.BizException;
import com.sgs.framework.model.enums.RefSystemIdEnum;
import com.sgs.framework.tool.utils.CollectionUtil;
import com.sgs.framework.tool.utils.Func;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.*;

/**
 * <AUTHOR>
 */
@Service
public class ConfigServiceImpl implements ConfigService {

    private static final Logger log = LoggerFactory.getLogger(ConfigServiceImpl.class);
    private static Multimap<Integer, String> groupCodeMap = ArrayListMultimap.create();

    static {
        groupCodeMap.put(RefSystemIdEnum.Shein.getRefSystemId(), "CG0001085");
        groupCodeMap.put(RefSystemIdEnum.SheinSupplier.getRefSystemId(), "CG0001085");
        groupCodeMap.put(RefSystemIdEnum.SEMIR.getRefSystemId(), "CG0000840");
        groupCodeMap.put(RefSystemIdEnum.ANTA.getRefSystemId(), "CG0000580");
        groupCodeMap.put(RefSystemIdEnum.FastFish.getRefSystemId(), "CG0000856");
        groupCodeMap.put(RefSystemIdEnum.Camel.getRefSystemId(), "CG0000858");
        groupCodeMap.put(RefSystemIdEnum.PeaceBird.getRefSystemId(), "CG0001231");
        groupCodeMap.put(RefSystemIdEnum.TIC.getRefSystemId(), "CG0000810");
        groupCodeMap.put(RefSystemIdEnum.LINING.getRefSystemId(), "CG0000563");
        groupCodeMap.put(RefSystemIdEnum.UNIQLO.getRefSystemId(), "CG0000237");
        groupCodeMap.put(RefSystemIdEnum.LOWES.getRefSystemId(), "CG0000846");
        groupCodeMap.put(RefSystemIdEnum.Septwolves.getRefSystemId(), "CG0001420");
        groupCodeMap.put(RefSystemIdEnum.F21.getRefSystemId(), "CG0000059");
        groupCodeMap.put(RefSystemIdEnum.Target.getRefSystemId(), "CG0000219");
        groupCodeMap.put(10028, "CG0000219");
    }

    @Resource
    private ConfigInfoDomainService configInfoDomainService;

    @Override
    public Optional<ConfigCustomerData> getCustomerData(Integer refSystemId) {
        return configInfoDomainService.selectCustomerData(refSystemId).map(po -> {
            ConfigCustomerData customerData = new ConfigCustomerData();
            customerData.setRefSystemId(po.getRefSystemId());
            customerData.setData(po.getData().toString());
            return customerData;
        });
    }

    @Override
    public ConfigInfo getConfig(ConfigGetReq configGetReq) {
        Assert.notNull(configGetReq);
        //至少要有product line level的配置
//        Assert.notNull(configGetReq.getProductLine());
        Assert.notNull(configGetReq.getConfigKey());
        if (Func.isBlank(configGetReq.getIdentityId()) && Func.isBlank(configGetReq.getCustomerGroup()) && Func.isBlank(configGetReq.getCustomerNo())) {
            throw new BizException("Request params error,IdentityId、customerGroupCode、customerNo Cannot be empty at the same time！");
        }

        ConfigInfoDO queryParams = BeanUtil.copyProperties(configGetReq, ConfigInfoDO.class);
        queryParams.setCsutomerGroup(configGetReq.getCustomerGroup());

        List<ConfigInfoDO> configInfoDOList = configInfoDomainService.select(queryParams);

        if (CollectionUtil.isEmpty(configInfoDOList)) {
            queryParams.setProductLine(null);
            configInfoDOList = configInfoDomainService.select(queryParams);
        }

        ConfigInfoDO configInfoDO = CollUtil.get(configInfoDOList, 0);

        //获取规则 TODO 后期根据 level
        // customerNo > customerGroupCode > identify > productLineCode

        return toConfigInfo(configInfoDO);
    }

    @Override
    public List<String> getSupportEmailList(Integer systemId) {
        List<String> list = new ArrayList<>();
        if (Func.isEmpty(systemId)) {
            return list;
        }
        try {
            ConfigInfoDO queryParams = new ConfigInfoDO();
            queryParams.setIdentityId(systemId.toString());
            queryParams.setConfigKey(Constants.CONFIG_SYSTEM_CONFIG);
            List<ConfigInfoDO> configInfoDOList = configInfoDomainService.select(queryParams);
            if (Func.isEmpty(configInfoDOList)) {
                return list;
            }
            ConfigInfoDO configInfoDO = configInfoDOList.get(0);
            String configValue = configInfoDO.getConfigValue();
            Object eval = JSONPath.eval(JSONObject.parseObject(configValue), "$.supportEmail");
            if (Func.isEmpty(eval)) {
                return list;
            }
            list = JSONArray.parseArray(eval.toString(), String.class);
        } catch (Exception e) {
            log.error(e.getMessage());
        }
        return list;
    }

    @Override
    public List<ConfigInfo> getConfigList(ConfigGetReq configGetReq) {
        Assert.notNull(configGetReq.getConfigKey());
        ConfigInfoDO queryParams = BeanUtil.copyProperties(configGetReq, ConfigInfoDO.class);
        queryParams.setCsutomerGroup(configGetReq.getCustomerGroup());
        List<ConfigInfoDO> configInfoDOList = configInfoDomainService.select(queryParams);
        List<ConfigInfo> configInfoList = new ArrayList<>();
        for (ConfigInfoDO ConfigInfoDO : configInfoDOList) {
            configInfoList.add(toConfigInfo(ConfigInfoDO));
        }
        return configInfoList;
    }

    private static ConfigInfo toConfigInfo(ConfigInfoDO configInfoDO) {
        if (Func.isEmpty(configInfoDO)) {
            return null;
        }
        ConfigInfo configInfo = new ConfigInfo();
        configInfo.setCsutomerGroup(configInfoDO.getCsutomerGroup());   
        configInfo.setCustomerNo(configInfoDO.getCustomerNo());
        configInfo.setProductLine(configInfoDO.getProductLine());
        configInfo.setConfigType(configInfoDO.getConfigType());
        configInfo.setConfigKey(configInfoDO.getConfigKey());
        configInfo.setConfigValue(configInfoDO.getConfigValue());
        configInfo.setIdentityId(configInfoDO.getIdentityId());
        return configInfo;
    }

    @Override
    public List<String> getCustomerIds(Integer refSystemId) {
        // TODO 此处后面等commonService的货币转换接口提供后应该废弃或修改，暂时因为土耳其shein场景，临时方案使用
        if (Objects.equals(refSystemId, 7)) {
            return Arrays.asList("4334450");
        }
        return new ArrayList<>();
    }

    @Override
    public List<String> getCustomerGroupCodeByRefSystemId(Integer refSystemId) {
        Collection<String> strings = groupCodeMap.get(refSystemId);
        if (Func.isEmpty(strings)) {
            return new ArrayList<>();
        }
        return (List<String>) strings;
    }

}
