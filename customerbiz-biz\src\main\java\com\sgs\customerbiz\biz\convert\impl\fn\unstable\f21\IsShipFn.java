package com.sgs.customerbiz.biz.convert.impl.fn.unstable.f21;

import cn.hutool.core.date.DateUtil;
import com.sgs.customerbiz.biz.convert.impl.fn.base.StringOperationFn;
import com.sgs.customerbiz.model.trf.enums.ConclusionCodeType;
import com.sgs.framework.tool.utils.Func;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Date 2024/4/2 19:38
 */
@Slf4j
@Component
public class IsShipFn extends StringOperationFn {

    private static final String reviewConclusion = "Override";

    private static final String YES = "Yes";

    private static final String NO = "No";

    @Override
    public Object invoke(Object[] arg1) {
        if (Func.isEmpty(arg1)) {
            return null;
        }

        Object argObject1 = arg1[0];
        Object argObject2 = arg1[1];

        String str1 = "";
        String str2 = "";
        if (Func.isNotEmpty(argObject1)) {
            str1 = argObject1.toString();
        }

        if (Func.isNotEmpty(argObject2)) {
            str2 = argObject2.toString();
        }

        if (Objects.equals(String.valueOf(ConclusionCodeType.Pass.getCode()), str1)
                || Objects.equals(ConclusionCodeType.Pass.getDesc(), str1)
                || Objects.equals(str2, reviewConclusion)) {
            return YES;
        }

        return NO;

    }


    @Override
    public String getName() {
        return "isShipFn";
    }

    @Override
    public String desc() {
        return "isShipFn";
    }
}
