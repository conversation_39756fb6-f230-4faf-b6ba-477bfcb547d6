package com.sgs.config.impl.domain;

import com.sgs.config.impl.domain.domainobject.ConfigInfoDO;
import com.sgs.customerbiz.dbstorages.mybatis.extmapper.todolist.CfgCustomerDataExtMapper;
import com.sgs.customerbiz.dbstorages.mybatis.mapper.todolist.ConfigInfoMapper;
import com.sgs.customerbiz.dbstorages.mybatis.model.CfgCustomerDataPO;
import com.sgs.customerbiz.dbstorages.mybatis.model.ConfigInfoExample;
import com.sgs.customerbiz.dbstorages.mybatis.model.ConfigInfoPO;
import com.sgs.framework.tool.utils.BeanUtil;
import com.sgs.framework.tool.utils.Func;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * @author: shawn.yang
 * @create: 2023-05-08 16:20
 */
@Service
public class ConfigInfoDomainService {

    @Resource
    private ConfigInfoMapper configInfoMapper;

    @Autowired
    private CfgCustomerDataExtMapper cfgCustomerDataMapper;

    public Optional<CfgCustomerDataPO> selectCustomerData(Integer refSystemId) {
        return Optional.ofNullable(cfgCustomerDataMapper.selectByRefSystemId(refSystemId));
    }

    public List<ConfigInfoDO> select(ConfigInfoDO configInfoDO) {
        ConfigInfoExample configInfoExample = new ConfigInfoExample();
        ConfigInfoExample.Criteria criteria = configInfoExample.createCriteria();
        criteria.andConfigKeyEqualTo(configInfoDO.getConfigKey());
        if (Func.isNotEmpty(configInfoDO.getCsutomerGroup())) {
            criteria.andCsutomerGroupEqualTo(configInfoDO.getCsutomerGroup());
        }
        if (Func.isNotEmpty(configInfoDO.getCustomerNo())) {
            criteria.andCustomerNoEqualTo(configInfoDO.getCustomerNo());

        }
        if (Func.isNotEmpty(configInfoDO.getConfigType())) {
            criteria.andConfigTypeEqualTo(configInfoDO.getConfigType());
        }
        if (Func.isNotEmpty(configInfoDO.getProductLine())) {
            criteria.andProductLineEqualTo(configInfoDO.getProductLine());
        }
        if (!ObjectUtils.isEmpty(configInfoDO.getIdentityId())) {
            criteria.andIdentityIdEqualTo(configInfoDO.getIdentityId());
        }
        List<ConfigInfoPO> configInfoPOList = configInfoMapper.selectByExample(configInfoExample);
        List<ConfigInfoDO> configList = configInfoPOList.stream()
                .map(configInfoPO -> BeanUtil.copyProperties(configInfoPO, ConfigInfoDO.class))
                .collect(Collectors.toList());
        return configList;
    }


}
