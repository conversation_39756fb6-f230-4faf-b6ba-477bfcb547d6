package com.sgs.config.api.service;

import com.sgs.config.api.dto.EventSubscribeDTO;

import java.util.List;
import java.util.Optional;

/**
 * @author: shawn.yang
 * @create: 2023-05-08 17:11
 */
public interface EventSubscribeService {

//    List<EventSubscribeDTO> findByRefSystemId(Integer refSystemId);


    List<EventSubscribeDTO> findByRefSystemIdAndEventCode(Integer refSystemId, Integer eventCode);

    List<EventSubscribeDTO> get(Integer refSystemId, Integer subscriber, Integer eventCode);

    List<EventSubscribeDTO> findBySubscriberAndEventCode(Optional<Long> apiId, Integer subscriber, Integer eventCode);

    List<EventSubscribeDTO> findBySubsciberAndRefSystemId(Integer subsciber, Integer refSystemId);

}
