package com.sgs.customerbiz.biz.convert.impl.fn;

import com.alibaba.fastjson.JSONArray;
import com.sgs.customerbiz.biz.convert.impl.fn.base.AbstractDataConvertFn;
import org.springframework.stereotype.Component;

import java.util.Objects;
import java.util.stream.Stream;

@Component
public class StringToJSONArrayFn extends AbstractDataConvertFn {

    @Override
    public Object invoke(Object[] args) {
        JSONArray jsonArray = new JSONArray();
        Stream.of(args).filter(Objects::nonNull).forEach(jsonArray::add);
        return jsonArray;
    }

    @Override
    public String getName() {
        return "stringToJSONArray";
    }

    @Override
    public String desc() {
        return "stringToJSONArrayFn";
    }
}
