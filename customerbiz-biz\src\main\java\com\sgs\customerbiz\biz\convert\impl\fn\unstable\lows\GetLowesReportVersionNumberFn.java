package com.sgs.customerbiz.biz.convert.impl.fn.unstable.lows;

import com.sgs.customerbiz.biz.convert.impl.fn.base.UnstableConvertFn;
import com.sgs.customerbiz.dbstorages.mybatis.model.TrfReportPO;
import com.sgs.customerbiz.domain.domainservice.TrfReportDomainService;
import com.sgs.framework.tool.utils.Func;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Date 2024/1/9 16:18
 */
@Slf4j
@Component
public class GetLowesReportVersionNumberFn implements UnstableConvertFn {

    @Autowired
    private TrfReportDomainService trfReportDomainService;

    @Override
    public Object invoke(Object[] args) {
        if (Func.isEmpty(args)) {
            return null;
        }
        if (Func.isEmpty(args[0]) || Func.isEmpty(args[1])) {
            return null;
        }
        List<TrfReportPO> list = trfReportDomainService.selectByTrfNo(args[0].toString(), Integer.parseInt(args[1].toString()));
        if (Func.isEmpty(list)) {
            return null;
        }
        return list.size();
    }

    @Override
    public String getName() {
        return "getLowesReportVersionNumberFn";
    }

    @Override
    public String desc() {
        return "getLowesReportVersionNumberFn";
    }
}
