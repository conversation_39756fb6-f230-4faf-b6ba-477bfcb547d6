package com.sgs.customerbiz.biz.convert.impl.fn.unstable.lows;

import com.sgs.customerbiz.biz.convert.impl.fn.base.UnstableConvertFn;
import com.sgs.framework.tool.utils.Func;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * <AUTHOR>
 * @Date 2024/1/9 19:29
 */
@Slf4j
@Component
public class LowsFileNameFn implements UnstableConvertFn {
    @Override
    public Object invoke(Object[] args) {
        if (Func.isEmpty(args)) {
            return null;
        }
        String arg1 = args[0].toString();
        Object arg2 = args[1];
        Object arg3 = args[2];
        Object arg4 = args[3];
        return null;
    }

    @Override
    public String getName() {
        return "lowsFileNameFn";
    }

    @Override
    public String desc() {
        return "lowsFileNameFn";
    }
}
