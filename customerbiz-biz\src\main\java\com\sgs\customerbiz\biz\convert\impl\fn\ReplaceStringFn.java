package com.sgs.customerbiz.biz.convert.impl.fn;

import com.sgs.customerbiz.biz.convert.impl.fn.base.StringOperationFn;
import org.springframework.stereotype.Component;

/**
 * @author: shawn.yang
 * @create: 2023-05-25 16:01
 */
@Component
public class ReplaceStringFn extends StringOperationFn {
    @Override
    public Object invoke(Object[] args) {
        if(args ==null){
            return null;
        }
        if (args.length != 2 && args.length != 3){
            throw new IllegalArgumentException(String.format("incorrect args length:expected 2~3 ,actual %d",args.length));
        }

        if (args.length ==3){
            return replaceAll(args[0].toString(),args[1].toString(),args[2].toString());
        }

        return subtractQuote(args[1].toString());
    }

    private String replaceAll(String origStr,String key,String replacement){
        origStr = subtractQuote(origStr);
        key = subtractQuote(key);
        replacement = subtractQuote(replacement);

        return origStr.replaceAll(key,replacement);
    }



    @Override
    public String getName() {
        return "replaceStr";
    }

    @Override
    public String desc() {
        return "功能：字符串替换;" +
                "接收两个参数:1.被要替换的值 2.需要替换的值";
    }
}
