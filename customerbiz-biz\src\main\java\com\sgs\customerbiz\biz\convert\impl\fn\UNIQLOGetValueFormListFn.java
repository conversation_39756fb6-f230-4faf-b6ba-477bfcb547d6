package com.sgs.customerbiz.biz.convert.impl.fn;

import cn.hutool.core.util.ArrayUtil;
import com.alibaba.fastjson.JSONObject;
import com.sgs.customerbiz.biz.convert.impl.fn.base.StringOperationFn;
import com.sgs.framework.tool.utils.Func;
import com.sgs.testdatabiz.facade.model.dto.rd.report.*;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

@Component
public class UNIQLOGetValueFormListFn extends StringOperationFn {

    private static final String PositionId = "positionId";
    private static final String AnalyteId = "analyteId";

    @Override
    public Object invoke(Object[] args) {
        if (ArrayUtil.isEmpty(args) || args.length<3) {
            return null;
        }
        Object key = args[0];
        if (Func.isEmpty(key)) {
            return null;
        }

        Object key2 = args[1];
        if (Func.isEmpty(key2)) {
            return null;
        }

        Object key3 = args[2];
        if (Func.isEmpty(key2)) {
            return null;
        }

        if (Objects.equals(key3.toString(), PositionId)) {
            RdReportMatrixDTO matrixDTO = JSONObject.parseObject(JSONObject.toJSONString(key), RdReportMatrixDTO.class);
            if (Func.isEmpty(matrixDTO)) {
                return null;
            }
            List<RdPositionDTO> positionList = matrixDTO.getPositionList();
            if (Func.isEmpty(positionList)) {
                return null;
            }
            RdPositionDTO positionDTO = positionList.stream().filter(l -> Objects.equals(l.getPositionInstanceId(), key2.toString())).findFirst().orElse(null);
            if (Func.isEmpty(positionDTO)) {
                return null;
            }
            return positionDTO.getPositionName();

        } else if (Objects.equals(key3.toString(), AnalyteId)) {
            RdTestLineDTO testLineDTO = JSONObject.parseObject(JSONObject.toJSONString(key), RdTestLineDTO.class);
            if (Func.isEmpty(testLineDTO)) {
                return null;
            }
            List<RdAnalyteDTO> analyteList = testLineDTO.getAnalyteList();
            if (Func.isEmpty(analyteList)) {
                return null;
            }
            RdAnalyteDTO rdAnalyteDTO = analyteList.stream().filter(l -> Objects.equals(l.getAnalyteInstanceId(), key2.toString())).findFirst().orElse(null);
            if (Func.isEmpty(rdAnalyteDTO)) {
                return null;
            }
            return rdAnalyteDTO.getAnalyteName();
        }
        return null;
    }


    @Override
    public String getName() {
        return "uNIQLOGetValueFormListFn";
    }

    @Override
    public String desc() {
        return "uNIQLOGetValueFormListFn";
    }
}
