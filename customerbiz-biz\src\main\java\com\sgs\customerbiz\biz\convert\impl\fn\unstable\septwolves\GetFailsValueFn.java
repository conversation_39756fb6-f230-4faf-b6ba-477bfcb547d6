package com.sgs.customerbiz.biz.convert.impl.fn.unstable.septwolves;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.sgs.customerbiz.biz.convert.impl.fn.base.UnstableConvertFn;
import com.sgs.customerbiz.model.trf.enums.ConclusionCodeType;
import com.sgs.framework.model.enums.LanguageType;
import com.sgs.framework.tool.utils.Func;
import com.sgs.testdatabiz.facade.model.dto.rd.report.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2024/1/9 16:18
 */
@Slf4j
@Component
public class GetFailsValueFn implements UnstableConvertFn {
    @Override
    public Object invoke(Object[] args) {
        if (Func.isEmpty(args) || args.length != 2) {
            return null;
        }
        if (Func.isEmpty(args[0]) || Func.isEmpty(args[1])) {
            return null;
        }

        Object reportObjet = args[0];
        Object testLineListObjet = args[1];
        RdReportDTO rdReportDTO = JSONObject.parseObject(reportObjet.toString(), RdReportDTO.class);
        List<RdTestLineDTO> testLineDTOList = JSONArray.parseArray(testLineListObjet.toString(), RdTestLineDTO.class);

        List<RdReportMatrixDTO> reportMatrixList = rdReportDTO.getReportMatrixList();
        if (Func.isEmpty(reportMatrixList)) {
            return null;
        }
        reportMatrixList = reportMatrixList.stream().filter(l -> {
            RdConclusionDTO conclusion = l.getConclusion();
            if (Func.isNotEmpty(conclusion) && (Objects.equals(String.valueOf(ConclusionCodeType.Fail.getCode()), conclusion.getConclusionCode())) || Objects.equals(ConclusionCodeType.Fail.getDesc(), conclusion.getConclusionCode())) {
                return true;
            }
            return false;
        }).collect(Collectors.toList());

        if (Func.isEmpty(reportMatrixList)) {
            return null;
        }

        List<String> testLineInstanceIdList = reportMatrixList.stream().map(RdReportMatrixDTO::getTestLineInstanceId).distinct().collect(Collectors.toList());

        testLineDTOList = testLineDTOList.stream().filter(l -> testLineInstanceIdList.contains(l.getTestLineInstanceId())).collect(Collectors.toList());

        if (Func.isEmpty(testLineDTOList)) {
            return null;
        }

        StringBuilder sb = new StringBuilder();

        for (int i = 0; i < testLineDTOList.size(); i++) {
            List<RdTestLineLanguageDTO> languageList = testLineDTOList.get(i).getLanguageList();
            if (Func.isEmpty(languageList) || Func.isEmpty(languageList.stream().filter(l -> Objects.equals(l.getLanguageId(), LanguageType.Chinese.getLanguageId())))) {
                sb.append(testLineDTOList.get(i).getEvaluationAlias());
                if (i < testLineDTOList.size() - 1) {
                    sb.append(",");
                }
            } else {
                RdTestLineLanguageDTO rdTestLineLanguageDTO = languageList.stream().filter(l -> Objects.equals(l.getLanguageId(), LanguageType.Chinese.getLanguageId())).findFirst().orElse(null);
                if (Func.isEmpty(rdTestLineLanguageDTO)) {
                    continue;
                }
                sb.append(rdTestLineLanguageDTO.getEvaluationAlias());
                if (i < testLineDTOList.size() - 1) {
                    sb.append(",");
                }
            }
        }
        return sb.toString();
    }

    @Override
    public String getName() {
        return "getFailsValueFn";
    }

    @Override
    public String desc() {
        return "getFailsValueFn";
    }
}
