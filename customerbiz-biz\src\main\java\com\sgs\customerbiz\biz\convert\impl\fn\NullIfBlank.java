package com.sgs.customerbiz.biz.convert.impl.fn;

import com.sgs.customerbiz.biz.convert.impl.fn.base.AbstractDataConvertFn;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Objects;

@Component
public class NullIfBlank extends AbstractDataConvertFn {

    @Override
    protected Object invoke(Object arg1) {
        if(Objects.isNull(arg1)) {
            return null;
        }
        return StringUtils.isBlank(arg1.toString()) ? null : arg1;
    }

    @Override
    public String getName() {
        return "nullIfBlank";
    }

    @Override
    public String desc() {
        return "nullIfBlankFn";
    }
}
