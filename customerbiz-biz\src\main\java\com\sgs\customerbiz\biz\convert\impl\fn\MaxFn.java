//package com.sgs.customerbiz.biz.service.convert.impl.fn;
//
//import com.sgs.customerbiz.biz.service.convert.impl.fn.base.DataConvertFn;
//
//import java.time.LocalDateTime;
//import java.util.List;
//import java.util.Objects;
//
///**
// * @author: shawn.yang
// * @create: 2023-06-07 11:18
// */
//public class MaxFn implements DataConvertFn {
//
//    @Override
//    public Object invoke(Object[] args) {
//        Object arg = args[0];
//
//        if (arg instanceof List){
//            List list = (List) arg;
//            if (list.isEmpty()){
//                return null;
//            }
//
//            boolean compareString = list.get(0) instanceof String;
//            if (compareString){
//                boolean isDate = isDate(list.get(0).toString());
//
//                if (isDate){
//                    return list.stream().filter(Objects::nonNull).map(s-> LocalDateTime.parse(s.toString())).max(LocalDateTime::compareTo)
//                }
//
////                list.stream().map(Object::toString).sorted().
//            }
//
//            return
//        }
//
//
//        return null;
//    }
//
//    private boolean isDate(String str){
//        try {
//            LocalDateTime.parse(str);
//            return true;
//        } catch (Exception e) {
//            return false;
//        }
//    }
//
//
//    @Override
//    public String getName() {
//        return "max";
//    }
//
//    @Override
//    public String desc() {
//        return null;
//    }
//}
