package com.sgs.config.api.dto.req;

import com.alibaba.fastjson.JSON;
import lombok.Data;

import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;

@Data
public class SendReportScheduled {

    private Integer identityId;

    private ReadRange readRange;

    private Duration duration;

    private SendMode sendMode;

    private int batchMaxSize;

    private boolean sendWhenEmpty = false;

    public enum ReadRange {
        Before, DayBefore, Duration
    }

    public enum SendMode {
        OneByOne, Batch
    }

    public DateRange dateRange() {

        switch (readRange) {
            case Before:
                return DateRange.before(new Date());
            case DayBefore:
                LocalDate now = LocalDate.now();
                LocalDate start = now.minusDays(1);
                Date startDateTime = Date.from(start.atStartOfDay().atZone(ZoneId.systemDefault()).toInstant());
                Date endDateTime = Date.from(now.atStartOfDay().atZone(ZoneId.systemDefault()).toInstant());
                return DateRange.range(startDateTime, endDateTime);
            case Duration:
                LocalDateTime nowTime = LocalDateTime.now();
                LocalDateTime startTime = nowTime.minusMinutes(duration.toMinutes());
                Date startDateTimeOfDuration = Date.from(startTime.atZone(ZoneId.systemDefault()).toInstant());
                Date endDateTimeOfDuration = Date.from(nowTime.atZone(ZoneId.systemDefault()).toInstant());
                return DateRange.range(startDateTimeOfDuration, endDateTimeOfDuration);
            default:
                throw new IllegalArgumentException("Unexpected Value: " + readRange);
        }
    }

    public static void main(String[] args) {
        SendReportScheduled sendReportScheduled = new SendReportScheduled();
        sendReportScheduled.setReadRange(ReadRange.Before);
        sendReportScheduled.setDuration(Duration.ofDays(1));
        sendReportScheduled.setSendMode(SendMode.OneByOne);
        sendReportScheduled.setBatchMaxSize(10);
//        String jsonString = JSON.toJSONString(sendReportScheduled);
        String jsonString = "{\"batchMaxSize\":10,\"readRange\":\"Before\",\"sendMode\":\"OneByOne\",\"skipRevise\":true,\"timeZone\":\"timeZone\",\"xxlJobId\":\"a id\"}";
        System.out.println(jsonString);
        SendReportScheduled send = JSON.parseObject(jsonString, SendReportScheduled.class);
        System.out.println(send);
    }

}
