package com.sgs.customerbiz.biz.convert.impl.fn;

import cn.hutool.core.util.ArrayUtil;
import com.alibaba.fastjson.JSONObject;
import com.sgs.customerbiz.biz.convert.impl.fn.base.StringOperationFn;
import com.sgs.customerbiz.biz.utils.ILayerMappingUtil;
import com.sgs.customerbiz.model.ext.dto.req.MappingBasicDataReq;
import com.sgs.customerbiz.model.ext.dto.rsp.GetBasicDataMappingRsp;
import com.sgs.framework.tool.utils.Func;
import com.sgs.testdatabiz.facade.model.dto.rd.report.RdAnalyteDTO;
import com.sgs.testdatabiz.facade.model.dto.rd.report.RdTestLineDTO;
import com.sgs.testdatabiz.facade.model.dto.rd.report.RdTestResultDTO;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;


@Component
public class UNIQLOUnitMappingFn extends StringOperationFn {

    private static final String UNIQLO_CUSTOMER_GROUP_CODE = "CG0000237";

    private static final String Analyte = "Unit";

    private static final Integer REF_SYSTEM_ID = 10017;

    @Override
    public Object invoke(Object[] args) {
        if (ArrayUtil.isEmpty(args)) {
            return null;
        }
        Object key = args[0];
        if (Func.isEmpty(key)) {
            return null;
        }

        Object key2 = args[1];
        if (Func.isEmpty(key2)) {
            return null;
        }

        Object key3 = args[2];
        if (Func.isEmpty(key3)) {
            return null;
        }

        RdTestResultDTO testResultDTO = JSONObject.parseObject(JSONObject.toJSONString(key), RdTestResultDTO.class);
        if (Func.isEmpty(testResultDTO)) {
            return null;
        }
        if (Func.isEmpty(testResultDTO.getTestResult().getTestResultFullNameRel())) {
            return null;
        }
        String analyteInstanceId = testResultDTO.getTestResult().getTestResultFullNameRel().getAnalyteInstanceId();
        if (Func.isBlank(analyteInstanceId)) {
            return null;
        }

        RdTestLineDTO testLineDTO = JSONObject.parseObject(JSONObject.toJSONString(key3), RdTestLineDTO.class);
        if (Func.isEmpty(testLineDTO)) {
            return null;
        }

        List<RdAnalyteDTO> analyteList = testLineDTO.getAnalyteList();
        if (Func.isEmpty(analyteList)) {
            return null;
        }

        RdAnalyteDTO rdAnalyteDTO = analyteList.stream().filter(l -> Objects.equals(analyteInstanceId, l.getAnalyteInstanceId())).findFirst().orElse(null);
        if (Func.isEmpty(rdAnalyteDTO)) {
            return null;
        }
        Long unitId = rdAnalyteDTO.getUnitId();
        if (Func.isEmpty(unitId)) {
            return null;
        }

        MappingBasicDataReq basicDataReq = new MappingBasicDataReq();
        basicDataReq.setCustomerGroupCode(UNIQLO_CUSTOMER_GROUP_CODE);
        basicDataReq.setProductLineCode(key2.toString());
        basicDataReq.setModuleType(Analyte);
        basicDataReq.setFilters(Arrays.asList(unitId.toString()));

        List<GetBasicDataMappingRsp> basicDataMapping = ILayerMappingUtil.getBasicDataMapping(basicDataReq);
        if (Func.isEmpty(basicDataMapping)) {
            return null;
        }

        return basicDataMapping.get(0).getCustomer().getUnitCode();
    }


    @Override
    public String getName() {
        return "uNIQLOUnitMappingFn";
    }

    @Override
    public String desc() {
        return "uNIQLOUnitMappingFn";
    }
}
