package com.sgs.customerbiz.biz.convert.impl.fn.unstable;

import com.sgs.customerbiz.biz.convert.impl.fn.base.UnstableConvertFn;
import com.sgs.customerbiz.dbstorages.mybatis.model.TrfReportPO;
import com.sgs.customerbiz.domain.domainservice.TrfReportDomainService;
import com.sgs.framework.core.exception.BizException;
import com.sgs.otsnotes.facade.model.enums.ReportStatus;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 根据originalReportNo区分revise的类型(这个函数偏TIC业务定制)
 * supplement/extract：1 （属于新增报告操作）
 * replace/rework ：2    （属于替换报告操作）
 * 输入（三个参数）：systemId 、 orderNo 、originalReportNo
 * @author: shawn.yang
 * @create: 2023-08-17 16:08
 */
@Slf4j
@Component
public class ReviseReportTypeFn implements UnstableConvertFn {
    // 新增报告操作
    private static final Integer NEW_REPORT_OPERATE = 1;

    // 替换报告操作
    private static final Integer REPLACE_REPORT_OPERATE = 2;

    private final TrfReportDomainService trfReportDomainService;

    public ReviseReportTypeFn(TrfReportDomainService trfReportDomainService) {
        this.trfReportDomainService = trfReportDomainService;
    }

    @Override
    public Object invoke(Object[] args) {
        if (args ==null || args.length != 3) {
            throw new IllegalArgumentException(String.format("incorrect args length:expected 3,actual %d",args ==null?0:args.length));
        }

        Object arg0 = args[0];
        Object arg1 = args[1];
        Object originalReportNo = args[2];
        Integer systemId = Integer.parseInt(arg0.toString());
        String orderNo = arg1.toString();

        // 没有originalReportNo 返回：新增报告
        boolean originalReportNoIsEmpty = (originalReportNo ==null) || (originalReportNo.toString().isEmpty());
        if (originalReportNoIsEmpty){
           return  NEW_REPORT_OPERATE;
        }

        // 查找原始报告状态
        Integer reportStatus = this.getOriginalReportStatus(systemId, orderNo, originalReportNo.toString());

        // 原报告状态不变的：返回1
        if (reportStatus.equals(ReportStatus.Completed.getCode()) || reportStatus.equals(ReportStatus.Approved.getCode())){
            return NEW_REPORT_OPERATE;
        }
        else if (reportStatus.equals(ReportStatus.Reworked.getCode()) || reportStatus.equals(ReportStatus.Replaced.getCode())
        ||reportStatus.equals(ReportStatus.Cancelled.getCode())){
            return REPLACE_REPORT_OPERATE;
        }

        log.error("ReviseReportTypeFn error,systemId:{} ,orderNo:{} ,originalReportNo:{} ,reportStatus:{}",systemId,orderNo,originalReportNo,reportStatus);
        return null;
    }

    @NotNull
    private Integer getOriginalReportStatus(Integer systemId, String orderNo, String originalReportNo) {
        List<TrfReportPO> trfReportList = trfReportDomainService.selectBySystemIdAndOrderNoAndReportNo(systemId, orderNo, originalReportNo);
        if (trfReportList.isEmpty()){
            throw new BizException(String.format("%d-%s, reportNo:%s not existed", systemId,orderNo,originalReportNo));
        }

        TrfReportPO trfReport = trfReportList.get(0);
        if (trfReport.getReportStatus() ==null){
            throw new BizException(String.format("%d-%s, reportNo:%s status can not be null", systemId,orderNo,originalReportNo));
        }
        return trfReport.getReportStatus();
    }

    @Override
    public String getName() {
        return "reviseReportType";
    }

    @Override
    public String desc() {
        return "根据originalReportNo区分revise的类型(这个函数偏TIC业务定制)\n" +
                " supplement/extract：1 （属于新增报告操作）\n" +
                " replace/rework ：2    （属于替换报告操作）\n" +
                " 输入（三个参数）：systemId 、 orderNo 、originalReportNo";
    }
}
