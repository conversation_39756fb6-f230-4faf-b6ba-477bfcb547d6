package com.sgs.customerbiz.biz.convert.impl.fn.unstable.septwolves;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.sgs.customerbiz.biz.convert.impl.fn.base.DataConvertFn;
import com.sgs.customerbiz.core.util.LOStringUtil;
import com.sgs.framework.model.enums.LanguageType;
import com.sgs.framework.tool.utils.Func;
import com.sgs.testdatabiz.facade.model.dto.rd.report.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * @Desc
 * <AUTHOR>
 * @date 2024/2/27 16:47
 */
@Slf4j
@Component
public class GetItemDetail4SeptwolvesFn implements DataConvertFn {
    @Autowired
    private GetTestResultFullName4SeptwolvesFn testResultFullName4SeptwolvesFn;

    @Override
    public Object invoke(Object[] args) {
        if (Func.isEmpty(args)) {
            return null;
        }
        List<Object> resultMap = new ArrayList<>();
        Object arg1 = args[0];
        Object arg2 = args[1];
        if (Func.isNotEmpty(arg1)) {
            List<RdTestResultDTO> testResultList = JSONArray.parseArray(JSONObject.toJSONString(arg1), RdTestResultDTO.class);
            testResultList.forEach(
                    l -> {
                        Map<String, Object> map = new HashMap<>();
                        map.put("ItemName", testResultFullName4SeptwolvesFn.invoke(new Object[]{JSONObject.toJSONString(l), 2}));
                        map.put("Result", arg2);
                        if (Func.isNotEmpty(l.getReportLimit()) && Func.isNotEmpty(l.getReportLimit().getLimitValueFullName())) {
                            map.put("StandValue", LOStringUtil.decode(l.getReportLimit().getLimitValueFullName()));
                        } else {
                            map.put("StandValue", "-");
                        }
                        if (Func.isNotEmpty(l.getTestResult()) && Func.isNotEmpty(l.getTestResult().getResultValue())) {
                            map.put("ActureValue", l.getTestResult().getResultValue());
                        } else {
                            map.put("ActureValue", "-");
                        }
                        resultMap.add(map);
                    }
            );
        } else {
            Object arg3 = args[2];
            if (Func.isEmpty(arg3)) {
                return null;
            }
            RdTestLineDTO rdTestLineDTO = JSONObject.parseObject(JSONObject.toJSONString(arg3), RdTestLineDTO.class);
            RdTestLineLanguageDTO rdTestLineLanguageDTO = rdTestLineDTO.getLanguageList().stream().filter(l -> Objects.equals(l.getLanguageId(), LanguageType.Chinese.getLanguageId())).findFirst().orElse(null);
            String itemName = null;
            if (Func.isEmpty(rdTestLineLanguageDTO)) {
                itemName = rdTestLineDTO.getEvaluationAlias();
            } else {
                itemName = rdTestLineLanguageDTO.getEvaluationAlias();
            }

            Map<String, Object> map = new HashMap<>();
            map.put("ItemName", itemName);
            map.put("Result", arg2);
            map.put("StandValue", "-");
            map.put("ActureValue", "-");
            resultMap.add(map);
        }
        return resultMap;
    }

    @Override
    public String getName() {
        return "getItemDetail4SeptwolvesFn";
    }

    @Override
    public String desc() {
        return "getItemDetail4SeptwolvesFn";
    }
}
