package com.sgs.customerbiz.biz.convert.impl.fn;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.sgs.customerbiz.biz.convert.impl.fn.base.StringOperationFn;
import com.sgs.framework.tool.utils.Func;
import com.sgs.testdatabiz.facade.model.dto.rd.report.RdReportDTO;
import com.sgs.testdatabiz.facade.model.dto.rd.report.RdReportMatrixDTO;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

@Component
public class GetReportByTestMatrixIdFn extends StringOperationFn {
    @Override
    public Object invoke(Object[] args) {
        if (Func.isEmpty(args)) {
            return null;
        }
        Object reportList = args[0];
        if (args.length == 1) {
            if (reportList instanceof List) {
                List<RdReportDTO> reportDTOList = JSONArray.parseArray(reportList.toString(), RdReportDTO.class);
                if (Func.isEmpty(reportDTOList)) {
                    return null;
                }
                return reportDTOList.get(0).getReportNo();
            } else {
                if (Func.isEmpty(reportList)) {
                    return null;
                }
                RdReportDTO rdReportDTO = JSONObject.parseObject(reportList.toString(), RdReportDTO.class);
                if (Func.isNotEmpty(rdReportDTO)) {
                    return rdReportDTO.getReportNo();
                }
                return null;
            }
        }
        if (args.length != 3) {
            return null;
        }
        Object testMatrixId = args[1];
        Object key = args[2];
        if (Func.isEmpty(reportList) || Func.isEmpty(testMatrixId)) {
            return null;
        }

        List<RdReportDTO> reportDTOList = JSONArray.parseArray(reportList.toString(), RdReportDTO.class);

        String testMatrixIdStr = testMatrixId.toString();

        for (RdReportDTO l : reportDTOList) {
            List<RdReportMatrixDTO> list = l.getReportMatrixList();
            if (Func.isEmpty(list)) {
                break;
            }

            long count = list.stream().filter(v -> Objects.equals(v.getTestMatrixId(), testMatrixIdStr)).count();
            if (count > 0) {
                switch (key.toString()) {
                    case "1":
                        return l.getReportNo();
                    case "2":
                        return l.getReportMatrixList().stream().filter(v -> Objects.equals(v.getTestMatrixId(), testMatrixIdStr)).findFirst().orElse(null).getTestSampleInstanceId();
                    case "3":
                        return l.getReportMatrixList().stream().filter(v -> Objects.equals(v.getTestMatrixId(), testMatrixIdStr)).findFirst().orElse(null).getTestLineInstanceId();
                    case "4":
                        return l.getReportMatrixList().stream().filter(v -> Objects.equals(v.getTestMatrixId(), testMatrixIdStr)).findFirst().orElse(null);
                }
                return l.getReportNo();
            }
        }

        return null;
    }

    @Override
    public String getName() {
        return "getReportByTestMatrixIdFn";
    }

    @Override
    public String desc() {
        return "getReportByTestMatrixIdFn";
    }
}
