package com.sgs.customerbiz.biz.convert.impl.fn.unstable;

import com.sgs.customerbiz.biz.convert.impl.fn.base.StringOperationFn;
import com.sgs.framework.tool.utils.Func;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Optional;

/**
 * 增强版填充函数，支持自定义填充字符和填充位置
 * 参数说明：
 * arg1: 原始字符串
 * arg2: 目标长度
 * arg3: 填充字符（默认为空格）
 * arg4: 填充位置（默认为right，可选值：left/right）
 *      - left: 在字符串前面填充
 *      - right: 在字符串后面填充
 */
@Slf4j
@Component
public class PaddingWith extends StringOperationFn {

    private static final String PADDING_POSITION_LEFT = "left";
    private static final String PADDING_POSITION_RIGHT = "right";

    @Override
    public Object invoke(Object[] args) {
        if (args.length < 2) {
            throw new IllegalArgumentException("padding requires at least 2 arguments: source string and target size");
        }

        Object arg1 = args[0]; // 原始字符串
        Object arg2 = args[1]; // 目标长度
        
        // 获取填充字符，默认为空格
        String paddingChar = args.length > 2 && args[2] != null ? 
            String.valueOf(args[2]).substring(0, 1) : " ";

        // 获取填充位置，默认为right
        String position = args.length > 3 && args[3] != null ? 
            String.valueOf(args[3]).toLowerCase() : PADDING_POSITION_RIGHT;

        // 验证填充位置参数
        if (!PADDING_POSITION_LEFT.equals(position) && !PADDING_POSITION_RIGHT.equals(position)) {
            throw new IllegalArgumentException("Invalid padding position. Must be either 'left' or 'right'");
        }

        if(Func.isEmpty(arg2)) {
            throw new IllegalArgumentException("padding size is empty");
        }

        int size = 0;
        try {
            size = Integer.parseInt(arg2.toString());
        } catch (NumberFormatException e) {
            throw new IllegalArgumentException("padding size is not integer");
        }

        String strVal = Optional.ofNullable(arg1).map(String::valueOf).orElse("");
        if(Func.isBlank(strVal)) {
            return "";
        }
        int len = strVal.length();
        
        if(len == size) {
            return strVal;
        }

        // 如果原始字符串长度不够目标长度，根据position参数决定在前面还是后面填充
        if (len < size) {
            StringBuilder sb = new StringBuilder();
            int paddingLength = size - len;

            if (PADDING_POSITION_LEFT.equals(position)) {
                // 在前面填充
                for (int i = 0; i < paddingLength; i++) {
                    sb.append(paddingChar);
                }
                sb.append(strVal);
            } else {
                // 在后面填充
                sb.append(strVal);
                for (int i = 0; i < paddingLength; i++) {
                    sb.append(paddingChar);
                }
            }
            return sb.toString();
        }

        // 如果原始字符串长度超过目标长度，截取指定长度的子串
        return strVal.substring(0, size);
    }

    @Override
    public String getName() {
        return "paddingWith";
    }

    @Override
    public String desc() {
        return "padding with custom character and position control";
    }
}
