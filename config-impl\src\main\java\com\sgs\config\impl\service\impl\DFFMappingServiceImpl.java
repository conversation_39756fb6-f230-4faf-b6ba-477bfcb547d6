package com.sgs.config.impl.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.sgs.config.api.dto.DFFMappingDTO;
import com.sgs.config.api.dto.req.ConfigGetReq;
import com.sgs.config.api.dto.req.DFFMappingQuery;
import com.sgs.config.api.service.DFFMappingService;
import com.sgs.customerbiz.dbstorages.mybatis.mapper.todolist.CfgDFFMappingMapper;
import com.sgs.customerbiz.dbstorages.mybatis.model.CfgDFFMappingExample;
import com.sgs.customerbiz.dbstorages.mybatis.model.CfgDFFMappingPO;
import com.sgs.framework.tool.utils.Func;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @Desc
 * <AUTHOR>
 * @date 2024/1/9 14:35
 */
@Slf4j
@Service
public class DFFMappingServiceImpl implements DFFMappingService {

    @Autowired
    private CfgDFFMappingMapper cfgDFFMappingMapper;

    @Override
    public List<DFFMappingDTO> getDFFMapping(ConfigGetReq configGetReq) {

        CfgDFFMappingExample example = new CfgDFFMappingExample();
        CfgDFFMappingExample.Criteria criteria = example.createCriteria();

        if(Func.isNotEmpty(configGetReq.getProductLine())) {
            criteria.andBuCodeEqualTo(configGetReq.getProductLine());
        }

        if(Func.isNotEmpty(configGetReq.getCustomerGroup())) {
            criteria.andCustomerGroupCodeEqualTo(configGetReq.getCustomerGroup());
        }

        if(Func.isNotEmpty(configGetReq.getCustomerNo())) {
            criteria.andCustomerNoEqualTo(configGetReq.getCustomerNo());
        }

        if(Func.isNotEmpty(configGetReq.getIdentityId())) {
            criteria.andRefSystemIdEqualTo(Integer.valueOf(configGetReq.getIdentityId()));
        }

        List<CfgDFFMappingPO> queryResultList = cfgDFFMappingMapper.selectByExample(example);

        if (Func.isEmpty(queryResultList)) {
            return new ArrayList<>();
        }

        return toDTO(queryResultList);
    }

    @NotNull
    private static List<DFFMappingDTO> toDTO(List<CfgDFFMappingPO> queryResultList) {
        List<DFFMappingDTO> dtoList = queryResultList.stream().map(item -> {
            DFFMappingDTO dffMappingDTO = new DFFMappingDTO();
            BeanUtils.copyProperties(item, dffMappingDTO);
            try {
                Map<String, String> valueMap = Optional.ofNullable(item.getValueMapping())
                        .map(valueMapping -> JSON.parseObject(valueMapping, new TypeReference<Map<String, String>>() {}))
                        .orElse(Collections.emptyMap());
                dffMappingDTO.setValueMap(valueMap);
            } catch (Throwable t) {
                dffMappingDTO.setValueMap(Collections.emptyMap());
            }
            return dffMappingDTO;
        }).collect(Collectors.toList());

        return dtoList;
    }

    @Override
    public List<DFFMappingDTO> getDFFMapping(DFFMappingQuery query) {
        CfgDFFMappingExample example = new CfgDFFMappingExample();
        CfgDFFMappingExample.Criteria criteria = example.createCriteria();

        if(Func.isNotEmpty(query.getProductLine())) {
            criteria.andBuCodeEqualTo(query.getProductLine());
        }

        if(Func.isNotEmpty(query.getCustomerGroup())) {
            criteria.andCustomerGroupCodeEqualTo(query.getCustomerGroup());
        }

        if(Func.isNotEmpty(query.getCustomerNo())) {
            criteria.andCustomerNoEqualTo(query.getCustomerNo());
        }

        if(Func.isNotEmpty(query.getIdentityId())) {
            criteria.andRefSystemIdEqualTo(Integer.valueOf(query.getIdentityId()));
        }

        if(Func.isNotEmpty(query.getTemplateId())) {
            criteria.andTemplateIdEqualTo(query.getTemplateId());
        }

        if(Func.isNotEmpty(query.getTemplateType())) {
            criteria.andTemplateTypeEqualTo(query.getTemplateType());
        }
        List<CfgDFFMappingPO> queryResultList = cfgDFFMappingMapper.selectByExample(example);

        if (Func.isEmpty(queryResultList)) {
            return new ArrayList<>();
        }
        return toDTO(queryResultList);
    }
}
