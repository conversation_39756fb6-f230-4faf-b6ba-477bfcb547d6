# API Request Recorder Exclusion Feature

## Overview

This feature allows you to configure which API methods should be excluded from recording based on method name and system ID. When a method matches the exclusion criteria, the `prepareLog` method will return `Optional.empty()`, preventing the API request from being recorded.

## Configuration

### Configuration Properties

The feature uses Spring Boot's `@ConfigurationProperties` to manage exclusion rules. The configuration is defined in `ApiRequestRecorderConfig.java`.

### Configuration Structure

```yaml
api:
  request:
    recorder:
      exclusion-methods:
        - method-name: "methodName1"
          system-id: "systemId1"
        - method-name: "methodName2"
          system-id: "systemId2"
```

### Configuration Properties

- `api.request.recorder.exclusion-methods`: List of exclusion method configurations
  - `method-name`: The exact method name to exclude (obtained from `joinPoint.getSignature().getName()`)
  - `system-id`: The system ID from request header (obtained from `HeaderHelper.getParamValue("systemId")`)

## Example Configuration

```yaml
# Example configuration for API Request Recorder exclusions
api:
  request:
    recorder:
      exclusion-methods:
        - method-name: "healthCheck"
          system-id: "10002"
        - method-name: "ping"
          system-id: "1"
        - method-name: "status"
          system-id: "10002"
```

## How It Works

1. **Method Interception**: The `ApiRequestRecorderAspect` intercepts method calls using AOP
2. **Exclusion Check**: Before creating the API request log, the aspect calls `shouldExcludeFromRecording()`
3. **Header Extraction**: The system ID is extracted from the request header using `HeaderHelper.getParamValue("systemId")`
4. **Method Name Extraction**: The method name is extracted from the join point using `joinPoint.getSignature().getName()`
5. **Matching Logic**: Both method name and system ID must match exactly for exclusion to apply
6. **Early Return**: If a match is found, `Optional.empty()` is returned, preventing recording

## Implementation Details

### Key Components

1. **ApiRequestRecorderConfig**: Configuration properties class
2. **ExclusionMethod**: Inner class representing a single exclusion rule
3. **shouldExcludeFromRecording()**: Method that performs the exclusion check

### Code Flow

```java
private Optional<ApiRequestPO> prepareLog(ProceedingJoinPoint joinPoint) {
    try {
        // Check if this method should be excluded from recording
        String methodName = joinPoint.getSignature().getName();
        String systemIdFromHeader = HeaderHelper.getParamValue(SYSTEM_ID);
        
        if (shouldExcludeFromRecording(methodName, systemIdFromHeader)) {
            return Optional.empty();
        }
        
        // Continue with normal logging logic...
    }
}
```

### Matching Logic

```java
private boolean shouldExcludeFromRecording(String methodName, String systemIdFromHeader) {
    if (apiRequestRecorderConfig == null || 
        apiRequestRecorderConfig.getExclusionMethods() == null || 
        apiRequestRecorderConfig.getExclusionMethods().isEmpty()) {
        return false;
    }
    
    return apiRequestRecorderConfig.getExclusionMethods().stream()
            .anyMatch(exclusion -> {
                boolean methodMatches = StringUtils.equals(methodName, exclusion.getMethodName());
                boolean systemIdMatches = StringUtils.equals(systemIdFromHeader, exclusion.getSystemId());
                return methodMatches && systemIdMatches;
            });
}
```

## Usage Notes

1. **Exact Matching**: Both method name and system ID must match exactly (case-sensitive)
2. **Configuration Reload**: Changes to configuration require application restart
3. **Null Safety**: The implementation handles null values gracefully
4. **Performance**: The exclusion check is performed early to minimize overhead
5. **Logging**: No additional logging is added for excluded methods to avoid noise

## Testing

To test the exclusion feature:

1. Configure exclusion rules in your application properties
2. Make API calls to methods that should be excluded
3. Verify that no API request records are created for excluded methods
4. Verify that non-excluded methods continue to be recorded normally

## Troubleshooting

- **Method not excluded**: Check that both method name and system ID match exactly
- **Configuration not loaded**: Verify YAML syntax and property names
- **System ID not found**: Ensure the request includes the `systemId` header
- **Method name mismatch**: Use `joinPoint.getSignature().getName()` to verify the actual method name
