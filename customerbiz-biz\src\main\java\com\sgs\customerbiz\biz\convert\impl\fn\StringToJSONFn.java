package com.sgs.customerbiz.biz.convert.impl.fn;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.sgs.customerbiz.biz.convert.impl.fn.base.StringOperationFn;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Objects;

@Component
public class StringToJSONFn extends StringOperationFn {

    @Override
    public Object invoke(Object[] args) {
        if(Objects.isNull(args) || args.length != 2) {
            throw new IllegalArgumentException("args is null or length not 2");
        }
        Object arg1 = args[0];
        Object arg2 = args[1];
        if(Objects.isNull(arg2)) {
            throw new IllegalArgumentException("arg2 is null");
        }

        if(Objects.isNull(arg1) || StringUtils.isBlank(arg1.toString())) {
            switch (arg2.toString()) {
                case "string": return "";
                case "true": return true;
                case "false": return false;
                case "number": return 0;
                case "object": return new JSONObject();
                case "array": return new JSONArray();
                default: return null;
            }
        }
        return JSON.parse(arg1.toString());
    }

    @Override
    public String getName() {
        return "stringToJSON";
    }

    @Override
    public String desc() {
        return "StringToJSONFn";
    }
}
