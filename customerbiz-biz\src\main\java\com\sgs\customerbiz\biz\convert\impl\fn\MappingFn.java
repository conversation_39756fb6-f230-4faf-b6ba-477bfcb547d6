package com.sgs.customerbiz.biz.convert.impl.fn;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.sgs.customerbiz.biz.convert.impl.fn.base.StringOperationFn;
import com.sgs.framework.tool.utils.Func;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 通用mapping，支持输入2~3个参数
 * param1： key
 * param2： 完整的mapping  e.g: 'SyncToOrder':64&'SyncConfirmed':14&'SyncCompleted':80&'PreOrderCancelTrf':91
 * param3： 默认值
 * @author: shawn.yang
 * @create: 2023-06-30 09:30
 */
@Component
public class MappingFn extends StringOperationFn {
    @Override
    public Object invoke(Object key,Object map) {
        return invoke(key,map,null);
    }

    @Override
    protected Object invoke(Object key, Object map, Object defaultValue) {
        if (key == null || map == null || Func.isEmpty(key)) {
            return defaultValue;
        }

        if (StringUtils.isAnyEmpty(key.toString(),map.toString())){
            throw new IllegalArgumentException("'key'/'map' can not empty");
        }

        //  从map中取值
        return mapping(key.toString(),map.toString(),defaultValue);
    }

    private Object mapping(String key, String map, Object defaultValue){
        if (map==null){
            throw new NullPointerException("MappingFn 'map' can not be null");
        }
        JSONObject jsonObject = JSON.parseObject(toJsonString(map));
        for (Map.Entry<String, Object> entry : jsonObject.entrySet()) {
            String mapKey = entry.getKey();
            if (wildcardMatch(mapKey, key)) {
                return entry.getValue();
            }
        }
        return defaultValue;
    }

    public static String wildcardToRegex(String wildcard) {
        StringBuilder s = new StringBuilder(wildcard.length());
        s.append('^');
        for (int i = 0, is = wildcard.length(); i < is; i++) {
            char c = wildcard.charAt(i);
            switch(c) {
                case '*':
                    s.append(".*");
                    break;
                case '?':
                    s.append(".");
                    break;
                // 如果有其他需要转义的字符，可以在这里添加
                default:
                    if (Pattern.compile("[\\\\.^$|()\\[\\]{}+]").matcher(String.valueOf(c)).find()) {
                        s.append("\\");
                    }
                    s.append(c);
                    break;
            }
        }
        s.append('$');
        return s.toString();
    }

    public static boolean wildcardMatch(String pattern, String text) {
        Pattern p = Pattern.compile(wildcardToRegex(pattern));
        Matcher m = p.matcher(text);
        return m.matches();
    }

    /**
     * @param arg   e.g:  'SyncToOrder':64&'SyncConfirmed':14&'SyncCompleted':80&'PreOrderCancelTrf':91
     * @return
     */
    private String toJsonString(String arg){
        return "{" + arg.replaceAll("&",",") + "}";
    }

    @Override
    public String getName() {
        return "mapping";
    }

    @Override
    public String desc() {
        return "通用mapping，支持输入2~3个参数\n" +
                " param1： key" +
                " 完整的mapping  e.g: 'SyncToOrder':64&'SyncConfirmed':14&'SyncCompleted':80&'PreOrderCancelTrf':91" +
                " param3： 默认值";
    }
}
