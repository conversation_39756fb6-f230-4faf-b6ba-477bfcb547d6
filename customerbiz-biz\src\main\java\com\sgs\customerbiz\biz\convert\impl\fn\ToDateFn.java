package com.sgs.customerbiz.biz.convert.impl.fn;

import cn.hutool.core.date.DateException;
import cn.hutool.core.date.DateUtil;
import com.sgs.customerbiz.biz.convert.impl.fn.base.StringOperationFn;
import com.sgs.framework.tool.utils.Func;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * @author: shawn.yang
 * @create: 2023-06-08 10:11
 */
@Slf4j
@Component
public class ToDateFn extends StringOperationFn {
    private static final String DEFAULT_FORMAT = "yyyy-MM-dd HH:mm:ss";

    @Override
    public Object invoke(Object date) {
        return formatDate(date, DEFAULT_FORMAT);
    }

    @Override
    public Object invoke(Object date, Object formatter) {
        return formatDate(date, StringUtils.defaultIfEmpty((String) formatter, DEFAULT_FORMAT));
    }

    @Override
    protected Object invoke(Object arg1, Object arg2, Object arg3) {
        if (Func.isEmpty(arg1)) {
            return null;
        }
        if (Func.isEmpty(arg3)) {
            return formatDate(arg1, Func.isEmpty(arg2) ? DEFAULT_FORMAT : arg2.toString());
        }
        Date date = com.sgs.framework.tool.utils.DateUtil.plusDays(formatDate(arg1), Integer.parseInt(arg3.toString()));
        return formatDate(date, StringUtils.defaultIfEmpty((String) arg2, DEFAULT_FORMAT));
    }

    private String formatDate(Object date, String format) {
        try {
            if (date == null) {
                return null;
            }
            format = subtractQuote(format);
            if (date instanceof Date) {
                return DateUtil.format((Date) date, format);
            } else if (date instanceof Long) {
                return DateUtil.format(new Date((long) date), format);
            } else if (date instanceof String) {
                try {
                    return DateUtil.format(DateUtil.parse(date.toString()), format);
                } catch (DateException parseError) {
                    // ignore
                }
                try {
                    SimpleDateFormat sdf = new SimpleDateFormat("MM/dd/yyyy");
                    return DateUtil.format(sdf.parse(date.toString()), format);
                } catch (Throwable t) {
                    // ignore
                }

                SimpleDateFormat sdf = new SimpleDateFormat("MM/yyyy");
                return DateUtil.format(sdf.parse(date.toString()), format);

            }
        } catch (Exception e) {
            log.error("ToDateFn parse date error. date:{} ,format:{}", date, format);
            throw new IllegalArgumentException("format date error");
        }

        log.error("unsupported date type. type:{}", date.getClass().getSimpleName());
        throw new IllegalArgumentException("unsupported date type");
    }

    private Date formatDate(Object date) {
        try {
            if (date == null) {
                return null;
            }
            if (date instanceof Date) {
                return (Date) date;
            } else if (date instanceof Long) {
                return new Date((long) date);
            } else if (date instanceof String) {
                return DateUtil.parse(date.toString());
            }
        } catch (Exception e) {
            log.error("ToDateFn parse date error. date:{} ,format:{}", date);
            throw new IllegalArgumentException("format date error");
        }
        log.error("unsupported date type. type:{}", date.getClass().getSimpleName());
        throw new IllegalArgumentException("unsupported date type");
    }


    @Override
    public String getName() {
        return "toDate";
    }

    @Override
    public String desc() {
        return "接收两个参数：" +
                "param1:时间,接收【时间戳、时间字符串、Date】三种类型  " +
                "param2:格式（可选，默认用‘yyyy-MM-dd HH:mm:ss’）";
    }
}
