package com.sgs.customerbiz.biz.convert.impl.fn;

import com.sgs.customerbiz.biz.convert.impl.fn.base.AbstractDataConvertFn;
import com.sgs.customerbiz.biz.convert.impl.fn.base.StringOperationFn;
import com.sgs.framework.tool.utils.Func;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

@Component
public class MergeFn extends StringOperationFn {

    private static final Integer ZERO = 0;

    @Override
    public Object invoke(Object[] args) {
        if (args == null || args.length == 0) {
            return null;
        }
        List list = new ArrayList();
        int length = args.length;
        String flagStr = args[length - 1].toString();
        String[] split = flagStr.split("&");


        if (split.length == 1) {
            for (int i = 0; i < length - 1; i++) {
                Object arg = args[i];
                if (Func.isEmpty(arg)) {
                    continue;
                }
                if (arg instanceof List) {
                    list.addAll((List) arg);
                } else {
                    list.add(arg);
                }
            }
        } else {
            for (int i = 0; i < length - 1; i++) {
                Object arg = args[i];
                if (Func.isEmpty(arg)) {
                    continue;
                }
                if (arg instanceof List) {
                    ((List<?>) arg).forEach(
                            l -> {
                                Map<String, Object> map = new HashMap<>();
                                map.put(split[1], l);
                                list.add(map);
                            }
                    );
                } else {
                    Map<String, Object> map = new HashMap<>();
                    map.put(split[1], arg);
                    list.add(map);
                }
            }
        }
        return list;
    }

    @Override
    public String getName() {
        return "mergeFn";
    }

    @Override
    public String desc() {
        return "mergeFn";
    }
}
