package com.sgs.customerbiz.biz.constants;

/**
 * <AUTHOR>
 * @Date 2023/6/13 10:27
 */
public class I18nErrorCodeConstants {


    // 10000 接口校验提醒

    // 字段必填校验
    public static final Integer ERROR_CODE_10001 = 10001;

    // 字段格式校验
    public static final Integer ERROR_CODE_10002 = 10002;

    // 字段枚举校验
    public static final Integer ERROR_CODE_10003 = 10003;

    // 字段数值校验
    public static final Integer ERROR_CODE_10004 = 10004;

    // 未来时间校验
    public static final Integer ERROR_CODE_10005 = 10005;

    // 过去时间校验
    public static final Integer ERROR_CODE_10006 = 10006;

    // 字段正数校验
    public static final Integer ERROR_CODE_10007 = 10007;

    // 字段非负校验
    public static final Integer ERROR_CODE_10008 = 10008;

    // 字段长度校验
    public static final Integer ERROR_CODE_10009 = 10009;


    // 20000 业务规则校验提醒


    // TRF是否允许导入
    public static final Integer ERROR_CODE_20001 = 20001;

    // TRF是否允许接收该状态
    public static final Integer ERROR_CODE_20002 = 20002;

    // TRF是否允许重复接收消息
    public static final Integer ERROR_CODE_20003 = 20003;

    // 缺少Remove Type
    public static final Integer ERROR_CODE_20004 = 20004;

    // 缺少Pending Type
    public static final Integer ERROR_CODE_20005 = 20005;

    // 缺少Cancel Type
    public static final Integer ERROR_CODE_20006 = 20006;

    // 缺少Cancel Reason
    public static final Integer ERROR_CODE_20007 = 20007;

    // TRF至少有一个Matrix信息
    public static final Integer ERROR_CODE_20008 = 20008;
    public static final String TRF_MATRIX_NOT_HAVE = "trf.matrix.not.have";

    // 每个子样必须有SKU No
    public static final Integer ERROR_CODE_20009 = 20009;
    public static final String TRF_SAMPLE_NOT_HAVE_SKU_NO = "trf.sample.not.have.sku.no";

    // 参与耐色互染测试的Mix样必须有Main Component
    public static final Integer ERROR_CODE_20010 = 20010;
    public static final String TRF_MIX_SAMPLE_NOT_HAVE_MAIN_COMPONENT = "trf.mix.sample.not.have.main.component";

    // TX单，原样、子样必须有Color
    public static final Integer ERROR_CODE_20011 = 20011;
    public static final String TRF_SAMPLE_TX_NOT_HAVE_COLOR = "trf.sample.tx.not.have.color";

    // TX+自建单，SKU Info中的Material Item和Material Cateory必填
    public static final Integer ERROR_CODE_20012 = 20012;
    public static final String TRF_SAMPLE_TX_SKU_INFO_NOT_HAVE = "trf.sample.tx.sku.info.not.have";

    // FW单，原样Color、Material、Age Group、Material SKU必
    public static final Integer ERROR_CODE_20013 = 20013;
    public static final String TRF_ORIGINAL_SAMPLE_SKU_INFO_NOT_HAVE = "trf.original.sample.sku.info.not.have";

    // FW单，子样Color、Material必填
    public static final Integer ERROR_CODE_20014 = 20014;
    public static final String TRF_SAMPLE_COLOR_MATERIAL_NOT_HAVE = "trf.sample.color.material.not.have";

    // FW+自建单，SKU Info中的Material Item和Material Cateory必填
    public static final Integer ERROR_CODE_20015 = 20015;
    public static final String TRF_SAMPLE_FW_SKU_INFO_NOT_HAVE = "trf.sample.fw.sku.info.not.have";

    // Mix样品中不能包含原样
    public static final Integer ERROR_CODE_20016 = 20016;
    public static final String TRF_MIX_SAMPLE_NOT_HAVE_ORIGINAL_SAMPLE = "trf.mix.sample.not.have.original.sample";

    // TL不允许有重复测点
    public static final Integer ERROR_CODE_20017 = 20017;
    public static final String TRF_TEST_LINE_CANNOT_HAVE_REPOINT = "trf.test.line.cannot.have.repoint";

    // TL只允许添加有Mapping关系的TL或为Pretreatment TL
    public static final Integer ERROR_CODE_20018 = 20018;
    public static final String TRF_TEST_LINE_MAPPING_PRETREATMENT_CAN_HAVE = "trf.test.line.mapping.pretreatment.can.have";

    // TL需要上传疵点图
    public static final Integer ERROR_CODE_20019 = 20019;
    public static final String TRF_TEST_LINE_NEED_SAMPLE_PHOTO = "trf.test.line.need.sample.photo";

    // Matrix不能有Share样参与
    public static final Integer ERROR_CODE_20020 = 20020;
    public static final String TRF_MATRIX_CANNOT_HAVE_SHARE_SAMPLE = "trf.matrix.cannot.have.share.sample";

    // 允许原样参与测试
    public static final Integer ERROR_CODE_20021 = 20021;
    public static final String TRF_ORIGINAL_SAMPLE_CAN_TEST = "trf.original.sample.can.test";

    // Quotation中的TL不允许出现Completed中未出现过的TL
    public static final Integer ERROR_CODE_20022 = 20022;
    public static final String TRF_TEST_LINE_QUOTATION_CANNOT_HAVE_COMPLETED_TEST_LINE = "trf.test.line.quotation.cannot.have.completed.test.line";

    // 每一个Matrix都必须有Test Data和Conclusion
    public static final Integer ERROR_CODE_20023 = 20023;
    public static final String TRF_MATRIX_TEST_DATA_CONCLUSION_NOT_HAVE = "trf.matrix.test.data.conclusion.not.have";

    // Conclusion必须为Pass、Fail、Data Only
    public static final Integer ERROR_CODE_20024 = 20024;
    public static final String TRF_MATRIX_CONCLUSION_IS_PASS_FAIL_DATA_ONLY = "trf.matrix.conclusion.is.pass.fail.data.only";
    public static final String TRF_TEST_LINE_CONCLUSION_IS_PASS_FAIL_DATA_ONLY = "trf.test.line.conclusion.is.pass.fail.data.only";
    public static final String TRF_TEST_SAMPLE_CONCLUSION_IS_PASS_FAIL_DATA_ONLY = "trf.test.sample.conclusion.is.pass.fail.data.only";
    public static final String TRF_REPORT_CONCLUSION_IS_PASS_FAIL_DATA_ONLY = "trf.report.conclusion.is.pass.fail.data.only";

    public static final String TRF_SAMPLE_CONCLUSION_CAN_NOT_BE_FAIL = "trf.sample.conclusion.can.not.be.fail";



    public static final String THE_DATA_MUST_BE_VALID = "the.data.must.be.valid";


}
