package com.sgs.customerbiz.biz.convert.impl.fn.unstable.septwolves;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.google.common.base.Joiner;
import com.sgs.customerbiz.biz.convert.impl.fn.base.UnstableConvertFn;
import com.sgs.testdatabiz.facade.model.dto.rd.report.RdAttrLanguageDTO;
import com.sgs.testdatabiz.facade.model.dto.rd.report.RdOrderDTO;
import com.sgs.testdatabiz.facade.model.dto.rd.report.RdProductSampleAttrDTO;
import com.sgs.testdatabiz.facade.model.dto.rd.report.RdSampleDTO;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

@Component
public class GetAllSampleSpecFn implements UnstableConvertFn {

    private static final String flag = ";";

    @Override
    public Object invoke(Object[] args) {
        if (args == null || args.length != 4) {
            return null;
        }

        Map<Integer, Integer> map = new HashMap<>();
        map.put(2, 1);
        map.put(1, 2);

        String arg2 = args[1].toString();
        String arg3 = args[2].toString();
        Integer arg4 = Integer.parseInt(args[3].toString());
        List<RdOrderDTO> rdOrderDTOS = JSON.parseObject(args[0].toString(), new TypeReference<List<RdOrderDTO>>() {
        });

        if(CollectionUtils.isEmpty(rdOrderDTOS)) {
            return null;
        }

        List<RdSampleDTO> sampleList = rdOrderDTOS.stream()
                .filter(order -> ! CollectionUtils.isEmpty(order.getSampleList()))
                .flatMap(order -> order.getSampleList().stream())
                .collect(Collectors.toList());


        List<RdSampleDTO> sampleDTOS = parseSampleDTOList(sampleList);
        if (sampleDTOS == null || sampleDTOS.isEmpty()) {
            return null;
        }

        Set<String> list = new HashSet<>();

        sampleDTOS.forEach(sampleDTO -> {
            String s = processSampleDTO(sampleDTO, arg2, arg3, arg4, map);
            list.add(s);
        });


        return Joiner.on("").join(list);
    }

    private List<RdSampleDTO> parseSampleDTOList(Object arg) {
        try {
            String str = null;
            if (arg instanceof List) {
                str = JSONObject.toJSONString(arg);
            } else {
                List list = new ArrayList();
                list.add(JSONObject.parseObject(arg.toString(), RdSampleDTO.class));
                str = JSONObject.toJSONString(list);
            }
            return JSONArray.parseArray(str, RdSampleDTO.class);
        } catch (Exception e) {
            return null;
        }
    }

    private String processSampleDTO(
            RdSampleDTO sampleDTO, String arg2, String arg3, Integer arg4,
            Map<Integer, Integer> map1) {
        List<RdProductSampleAttrDTO> sampleAttrList = sampleDTO.getSampleAttrList();

        String s1 = processAttributeList(sampleAttrList, arg2, arg4, map1, true);
        String s2 = processAttributeList(sampleAttrList, arg3, arg4, map1, false);
        return s1 + s2 + flag;
    }

    private String processAttributeList(
            List<RdProductSampleAttrDTO> attrList, String labelCode, Integer arg4,
            Map<Integer, Integer> map, boolean flag) {
        RdProductSampleAttrDTO k = attrList.stream()
                .filter(v -> Objects.equals(v.getLabelCode(), labelCode))
                .findFirst().orElse(null);

        List<RdAttrLanguageDTO> languageList = k.getLanguageList();
        RdAttrLanguageDTO rdAttrLanguageDTO = findLanguageDTO(languageList, arg4);

        if (rdAttrLanguageDTO == null) {
            rdAttrLanguageDTO = findLanguageDTO(languageList, map.get(arg4));
        }

        if (rdAttrLanguageDTO != null) {
            if (flag) {
                return "(" + rdAttrLanguageDTO.getValue() + ")";
            } else {
                return rdAttrLanguageDTO.getValue();
            }
        }
        return null;

    }

    private RdAttrLanguageDTO findLanguageDTO(List<RdAttrLanguageDTO> languageList, Object languageId) {
        return languageList.stream()
                .filter(k -> Objects.equals(k.getLanguageId(), languageId))
                .findFirst().orElse(null);
    }

    @Override
    public String getName() {
        return "getSampleSpec";
    }

    @Override
    public String desc() {
        return "getSampleSpec";
    }
}
