package com.sgs.customerbiz.biz.convert.impl.fn.unstable.tic;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONPath;
import com.sgs.customerbiz.biz.convert.impl.DataConvertContextHolder;
import com.sgs.customerbiz.biz.convert.impl.JsonDataConvertor;
import com.sgs.customerbiz.biz.convert.impl.fn.base.StringOperationFn;
import com.sgs.customerbiz.biz.convert.impl.fn.base.UnstableConvertFn;
import com.sgs.framework.tool.utils.Func;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;

@Component
public class TicGetSampleListFn extends StringOperationFn implements UnstableConvertFn {

    @Override
    public Object invoke(Object[] args) {
        if (args.length < 1) {
            throw new IllegalArgumentException();
        }
        Object arg = args[0];
        if (Func.isEmpty(arg)) {
            return null;
        }

        Map<Object, List<Object>> map = (Map) arg;

        List<JSONObject> sampleList = new ArrayList<>();

        JSONObject sourceObj = new JSONObject();

        Set<Object> keySet = map.keySet();
        for (Object key : keySet) {
            JSONObject sample = new JSONObject();
            if (Func.isNotEmpty(args[1])) {
                sample.put("templateId", args[1]);
            }
            sample.put("sampleNo", key);
            List<Object> list = map.get(key);
            if (Func.isNotEmpty(list)) {
                for (Object o : list) {
                    JSONObject jsonObject = (JSONObject)o;
                    sample.put((String) jsonObject.get("sampleKey"), jsonObject.get("sampleValue"));
                }
            }
            sampleList.add(sample);
        }
        sourceObj.put("sampleList", sampleList);

        JSONObject sourceData = DataConvertContextHolder.getSourceData();
        if (Func.isNotEmpty(sourceObj)) {
            sourceData.put("ticSampleList", sampleList);
            DataConvertContextHolder.setSourceData(sourceData);
        }

        return null;
    }

    @Override
    public String getName() {
        return "ticGetSampleListFn";
    }

    @Override
    public String desc() {
        return null;
    }
}
